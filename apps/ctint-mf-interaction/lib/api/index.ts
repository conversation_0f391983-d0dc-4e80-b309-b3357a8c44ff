import { addLoggingToAxios } from '@cdss-modules/design-system/lib/logging';
import { apiConfig } from './config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

type UserConfigItems = {
  name: string;
  value: string;
};

type UserConfigProps = {
  filters?: UserConfigItems[];
  columns?: UserConfigItems[];
};

enum FilterCondition {
  mediaType = 'mediaType',
  users = 'users',
  conversationId = 'conversationId',
  ani = 'ani',
  dnis = 'dnis',
  queues = 'queues',
  conversationDuration = 'conversationDuration',
  recordingMediaSource = 'recordingMediaSource',
  customerRemote = 'customerRemote',
  wrapups = 'wrapups',
  provider = 'provider',
  recording = 'recording',
  direction = 'direction',
}

export const axiosInstance = axios.create({
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-interaction',
    previousId: 'ctint-bff-cdss',
  },
});

export const axiosDownloadInstance = axios.create({
  timeout: 20000,
  headers: {
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-interaction',
    previousId: 'ctint-bff-cdss',
  },
});

axiosDownloadInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', uuidv4());
      localStorage.setItem(
        'gc-access-token',
        '4ne850UXVwj7J1oqS0yP05IqNLxnE_i81-js3tXdf5uCkuOQHE737vRCO8wHvXLC-VNZKOUnUctkT7k8RyvYbg'
      );
      localStorage.setItem(
        'cdss-auth-token',
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ccba';
    if (cdssAuthToken) {
      config.headers['cdss_authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (gcAccessToken) {
      config.headers['gc_authorization'] = 'Bearer ' + gcAccessToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }
    config.headers['mediaSource'] = 'aws'; // TODO: should get from config
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

axiosInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', 'ada46e22-7c55-4db0-8d99-1f33c183ac4d');
      localStorage.setItem(
        'gc-access-token',
        'kmUyiFx4vxTdQIGN6kQwlk7m-plxwmA_Df3Ivwob19UqzXfP6xiOx55f-QN_tjbln3DfsVDaaq1yoECaNI4-BA'
      );
      localStorage.setItem(
        'cdss-auth-token',
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuYGOs5VYUcdrgYQnON7IKcIFOoqgk9M='
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ccba';
    if (cdssAuthToken) {
      config.headers['cdss_authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (gcAccessToken) {
      config.headers['gc_authorization'] = 'Bearer ' + gcAccessToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }

    if (config.url?.includes('/interaction/recordings/transcript/')) {
      config.headers['requester'] = 'agent1';
    }

    console.log('axiosInstance.interceptors.request', config);
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export const fireGetFilteredRecordings = (
  queryDatas: object | undefined,
  basePath = ''
) => {
  // fix: if the queryDatas does not contain conversationStart and conversationEnd, will not call the api
  if (
    queryDatas &&
    !('conversationStart' in queryDatas) &&
    !('conversationEnd' in queryDatas)
  )
    return;

  // call api to for GC
  return fireGCRecordingsByFilters(queryDatas, basePath);

  // if (
  //   queryParams.includes('isInbound') ||
  //   queryParams.includes('isOutbound') ||
  //   queryParams.includes('duration') ||
  //   queryParams.includes('order')
  // ) {
  //   return fireGetSortedRecordings(queryParams, basePath);
  // } else if (queryParams.includes('id')) {
  //   const urlParams = new URLSearchParams(queryParams);
  //   const id = urlParams?.get('id') ?? '';

  //   return fireGetSingleRecording(id, basePath);
  // } else {
  //   return fireGetAllRecordings(queryParams, basePath);
  // }
};

export const fireGetSortedRecordings = (queryParams: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.sort}?${queryParams}`);

export const fireGetAllRecordings = (queryParams: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.recordings}?${queryParams}`);

export const fireGetSingleRecordingStream = (
  recordingId: string,
  basePath = ''
) =>
  axiosDownloadInstance({
    method: 'GET',
    url: basePath + `${apiConfig.paths.recordings}/${recordingId}`,
    responseType: 'blob',
  });

export const fireGetSingleRecording = (id: string, basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.detail.info}/${id}`);

export const fireGetRecordingMedia = (conversationId: string, basePath = '') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.detail.media}/${conversationId}`
  );

export const fireGetTranscript = (conversationId: string, basePath = '') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.detail.transcript}/${conversationId}`
  );

export const fireExportRecordings = (conversationId: string[], basePath = '') =>
  axiosInstance.post(`${basePath}${apiConfig.paths.export}/recording/export`, {
    conversationId,
  });

/**
 *  Download recordings as a zip file.
 *
 *  @param {string[]} conversationId The list of conversation IDs.
 *  @param {string} [basePath] The base URL of the API.
 *  @param {() => void} callback The callback function to be called after the download is completed.
 */
export const downloadRecordingZipFile = (
  conversationId: string[],
  basePath = '',
  callback: () => void
) =>
  axiosDownloadInstance({
    method: 'POST',
    url: basePath + apiConfig.paths.export + `/recording/export`,
    responseType: 'blob',
    data: {
      conversationId,
    },
  })
    .then((response) => {
      // 从响应头中提取 Content-Disposition
      const contentDisposition = response.headers['content-disposition'];
      let fileName = 'default-filename.zip'; // 默认文件名

      // 使用正则表达式提取文件名
      if (contentDisposition) {
        const matches = contentDisposition.match(/filename=([^;]+)/);
        if (matches && matches[1]) {
          fileName = matches[1].trim(); // 确保去除多余的空格
        }
      }
      // 创建一个 Blob URL 并触发下载
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName); // 使用从响应头中提取的文件名
      document.body.appendChild(link);
      link.click();
      link.remove(); // 下载完成后移除 link 元素
      console.info('文件下载成功！文件名为：', fileName);
    })
    .catch((error) => {
      console.error('文件下载失败：', error);
    })
    .finally(() => {
      callback();
    });

export const fireGetRecordingTranscript = (mediaUri: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.transcript}`, {
    timeout: 300000,
  });

export const fireGetUserConfig = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.config}`);

export const fireCreateUserConfig = (data: UserConfigProps, basePath = '') =>
  axiosInstance.post(`${basePath}${apiConfig.paths.config}`, data);

export const fireUpdateUserConfig = (data: UserConfigProps, basePath = '') =>
  axiosInstance.put(`${basePath}${apiConfig.paths.config}`, data);

export const fireGCRecordingsByFilters = (data: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.gc_recordings}`,
    data
  );
};

export const fireGetEvaluationFormTemplate = (basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.detail.evaluation_form}`
  );

  // Dummy data
  // return Promise.resolve({
  //   data: {
  //     data: [
  //       {
  //         formId: '1',
  //         formName: 'Customer Service Evaluation Form (V2024-04-15)',
  //         category: 'category_1',
  //         type: 'type_1',
  //         language: 'language_1',
  //         formVersion: 'version_2024-04-15',
  //       },
  //       {
  //         formId: '2',
  //         formName: 'Customer Service Evaluation Form (V2024-04-15)',
  //         category: 'category_1',
  //         type: 'type_1',
  //         language: 'language_1',
  //         formVersion: 'version_2024-06-25',
  //       },
  //       {
  //         formId: '3',
  //         formName: 'Sales Evaluation Form',
  //         category: 'category_2',
  //         type: 'type_2',
  //         language: 'language_2',
  //         formVersion: 'version_2024-03-01',
  //       },
  //     ],
  //   },
  // });
};

export const fireGetQmEvaluationList = (recordingId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.detail.evaluation_list}?recordingId=${recordingId}`
  );
  // return Promise.resolve({
  //   data: {
  //     data: [
  //       {
  //         evaluationId: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000001',
  //         formId: '1',
  //         formName: 'Customer Service Evaluation Form',
  //         formVersion: 'version_2024-04-15',
  //         evaluatorName: 'Peter Parker',
  //         score: 0,
  //         status: 'published', // 'init' | 'inprogress' | 'completed' | 'failed' | 'published'
  //         finalResult: 'pass', // 'pass' | 'fail'
  //         releasedTime: '2024-07-01T12:00:00Z',
  //         processBy: 'system',
  //       },
  //       {
  //         evaluationId: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000002',
  //         formId: '2',
  //         formName: 'Customer Service Evaluation Form',
  //         formVersion: 'version_2024-06-25',
  //         evaluatorName: 'Peter Parker',
  //         score: 0,
  //         status: 'completed', // 'init' | 'inprogress' | 'completed' | 'failed' | 'published'
  //         finalResult: 'fail', // 'pass' | 'fail'
  //         releasedTime: '2024-07-01T12:00:00Z',
  //         processBy: 'system',
  //       },
  //       {
  //         evaluationId: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000003',
  //         formId: '3',
  //         formName: 'Customer Service Evaluation Form',
  //         formVersion: 'version_2024-06-25',
  //         evaluatorName: 'Peter Parker',
  //         score: 0,
  //         status: 'inprogress', // 'init' | 'inprogress' | 'completed' | 'failed' | 'published'
  //         finalResult: '', // 'pass' | 'fail'
  //         releasedTime: '2024-07-01T12:00:00Z',
  //         processBy: 'system',
  //       },
  //     ],
  //   },
  // });
};

export const fireAssignEvaluationFormTemplate = (
  data: { recordingId: string; formId: string; interactionId: string },
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.detail.evaluation_assign}`,
    data
  );
  // return Promise.resolve({
  //   data: {
  //     isSuccess: true,
  //   },
  // });
};

export const fireUpdateNLPEvaluationResult = (
  data: {
    evaluationId: string;
    nlpResultId: string;
    manualResult: string;
    comment: string;
  },
  basePath = ''
) => {
  return axiosInstance.put(
    `${basePath}${apiConfig.paths.detail.evaluation_nlp_result_update}`,
    data
  );

  // return Promise.resolve({
  //   data: {
  //     isSuccess: true,
  //   },
  // });
};

export const fireUpdateEvaluationResult = (
  data: {
    evaluationId: string;
    manualResult: string;
    comment: string;
  },
  basePath = ''
) => {
  return axiosInstance.put(
    `${basePath}${apiConfig.paths.detail.evaluation_result_update}`,
    data
  );
  // return Promise.resolve({
  //   data: {
  //     data: [
  //       {
  //         metaDataResult: 'test',
  //         finalRate: 92,
  //         finalResult: 'Passed',
  //         manualResult: '',
  //         comment: '',
  //         createTime: '2024-10-14T03:31:48.961Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //         evaluationId: '4f93500a-f382-4492-a5c8-db9b092d6339',
  //         extractionResult: 'test',
  //         similarityResult: 'test',
  //         standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         transcriptMasterId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         nplResultId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         timerStart: '00:00:00.27',
  //         timerEnd: '00:00:02.83',
  //         standardScript: {
  //           standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //           formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //           stepId: '1',
  //           scenarioId: '1_1',
  //           content: 'Confirm if the recording has already started.',
  //           helpContent: '',
  //           participant: 'Agent',
  //           doSimilarity: true,
  //           doExtraction: true,
  //           createTime: '2024-10-11T08:09:43.872Z',
  //           updateTime: '0001-01-01T00:00:00Z',
  //           createBy: '',
  //           updateBy: '',
  //           platform: '',
  //           tenant: 'CCB',
  //         },
  //       },
  //       {
  //         metaDataResult: 'test',
  //         finalRate: 12,
  //         finalResult: 'Failed',
  //         manualResult: '',
  //         comment: '',
  //         createTime: '2024-10-14T03:31:48.961Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //         evaluationId: '4f93500a-f382-4492-a5c8-db9b092d6339',
  //         extractionResult: 'test',
  //         similarityResult: 'test',
  //         standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         transcriptMasterId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         nplResultId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         timerStart: '00:00:02.94',
  //         timerEnd: '00:00:04.379',
  //         standardScript: {
  //           standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //           formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //           stepId: '1',
  //           scenarioId: '1_2',
  //           content: 'Provide date and employee name.',
  //           helpContent: '',
  //           participant: 'Agent',
  //           doSimilarity: true,
  //           doExtraction: true,
  //           createTime: '2024-10-11T08:09:43.872Z',
  //           updateTime: '0001-01-01T00:00:00Z',
  //           createBy: '',
  //           updateBy: '',
  //           platform: '',
  //           tenant: 'CCB',
  //         },
  //       },
  //       {
  //         metaDataResult: 'test',
  //         finalRate: 92,
  //         finalResult: 'Passed',
  //         manualResult: 'Passed',
  //         comment: 'manual comment test',
  //         createTime: '2024-10-14T03:31:48.961Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //         evaluationId: '4f93500a-f382-4492-a5c8-db9b092d6339',
  //         extractionResult: 'test',
  //         similarityResult: 'test',
  //         standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         transcriptMasterId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         nplResultId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         standardScript: {
  //           standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //           formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //           stepId: '2',
  //           scenarioId: '2_1',
  //           content: 'Request the customer to provide their full name.',
  //           helpContent: '',
  //           participant: 'Agent',
  //           doSimilarity: true,
  //           doExtraction: true,
  //           createTime: '2024-10-11T08:09:43.872Z',
  //           updateTime: '0001-01-01T00:00:00Z',
  //           createBy: '',
  //           updateBy: '',
  //           platform: '',
  //           tenant: 'CCB',
  //         },
  //       },
  //       {
  //         metaDataResult: 'test',
  //         finalRate: 12,
  //         finalResult: 'Failed',
  //         manualResult: '',
  //         comment: '',
  //         createTime: '2024-10-14T03:31:48.961Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //         evaluationId: '4f93500a-f382-4492-a5c8-db9b092d6339',
  //         extractionResult: 'test',
  //         similarityResult: 'test',
  //         standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         transcriptMasterId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         nplResultId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         standardScript: {
  //           standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //           formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //           stepId: '2',
  //           scenarioId: '2_2',
  //           content:
  //             'Request the customer to provide the last four digits of their ID number.',
  //           helpContent: '',
  //           participant: 'Agent',
  //           doSimilarity: true,
  //           doExtraction: true,
  //           createTime: '2024-10-11T08:09:43.872Z',
  //           updateTime: '0001-01-01T00:00:00Z',
  //           createBy: '',
  //           updateBy: '',
  //           platform: '',
  //           tenant: 'CCB',
  //         },
  //       },
  //       {
  //         metaDataResult: 'test',
  //         finalRate: 22,
  //         finalResult: 'Failed',
  //         manualResult: '',
  //         comment: '',
  //         createTime: '2024-10-14T03:31:48.961Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //         evaluationId: '4f93500a-f382-4492-a5c8-db9b092d6339',
  //         extractionResult: 'test',
  //         similarityResult: 'test',
  //         standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         transcriptMasterId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         nplResultId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         standardScript: {
  //           standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //           formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //           stepId: '2',
  //           scenarioId: '2_3',
  //           content:
  //             'Request customers to provide their date of birth and address.',
  //           helpContent: '',
  //           participant: 'Agent',
  //           doSimilarity: true,
  //           doExtraction: true,
  //           createTime: '2024-10-11T08:09:43.872Z',
  //           updateTime: '0001-01-01T00:00:00Z',
  //           createBy: '',
  //           updateBy: '',
  //           platform: '',
  //           tenant: 'CCB',
  //         },
  //       },
  //     ],
  //   },
  // });
};

export const fireGetEvaluationResult = (
  evaluationId: string | null | undefined,
  currentRecordingId: string | null | undefined,
  basePath = ''
) => {
  if (!evaluationId) return;
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.detail.evaluation_nlp_result_list}?evaluationId=${evaluationId}&recordingId=${currentRecordingId}`
  );
  // return Promise.resolve({
  //   data: {
  //     data: [
  //       {
  //         metaDataResult: 'test',
  //         finalRate: 92,
  //         finalResult: 'Passed',
  //         manualResult: '',
  //         comment: '',
  //         createTime: '2024-10-14T03:31:48.961Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //         evaluationId: '4f93500a-f382-4492-a5c8-db9b092d6339',
  //         extractionResult: 'test',
  //         similarityResult: 'test',
  //         standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         transcriptMasterId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         nplResultId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         timerStart: '00:00:00.27',
  //         timerEnd: '00:00:02.83',
  //         standardScript: {
  //           standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //           formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //           stepId: '1',
  //           scenarioId: '1_1',
  //           content: 'Confirm if the recording has already started.',
  //           helpContent: '',
  //           participant: 'Agent',
  //           doSimilarity: true,
  //           doExtraction: true,
  //           createTime: '2024-10-11T08:09:43.872Z',
  //           updateTime: '0001-01-01T00:00:00Z',
  //           createBy: '',
  //           updateBy: '',
  //           platform: '',
  //           tenant: 'CCB',
  //         },
  //       },
  //       {
  //         metaDataResult: 'test',
  //         finalRate: 12,
  //         finalResult: 'Failed',
  //         manualResult: '',
  //         comment: '',
  //         createTime: '2024-10-14T03:31:48.961Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //         evaluationId: '4f93500a-f382-4492-a5c8-db9b092d6339',
  //         extractionResult: 'test',
  //         similarityResult: 'test',
  //         standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         transcriptMasterId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         nplResultId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         timerStart: '00:00:02.94',
  //         timerEnd: '00:00:04.379',
  //         standardScript: {
  //           standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //           formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //           stepId: '1',
  //           scenarioId: '1_2',
  //           content: 'Provide date and employee name.',
  //           helpContent: '',
  //           participant: 'Agent',
  //           doSimilarity: true,
  //           doExtraction: true,
  //           createTime: '2024-10-11T08:09:43.872Z',
  //           updateTime: '0001-01-01T00:00:00Z',
  //           createBy: '',
  //           updateBy: '',
  //           platform: '',
  //           tenant: 'CCB',
  //         },
  //       },
  //       {
  //         metaDataResult: 'test',
  //         finalRate: 92,
  //         finalResult: 'Passed',
  //         manualResult: 'Passed',
  //         comment: 'manual comment test',
  //         createTime: '2024-10-14T03:31:48.961Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //         evaluationId: '4f93500a-f382-4492-a5c8-db9b092d6339',
  //         extractionResult: 'test',
  //         similarityResult: 'test',
  //         standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         transcriptMasterId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         nplResultId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         standardScript: {
  //           standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //           formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //           stepId: '2',
  //           scenarioId: '2_1',
  //           content: 'Request the customer to provide their full name.',
  //           helpContent: '',
  //           participant: 'Agent',
  //           doSimilarity: true,
  //           doExtraction: true,
  //           createTime: '2024-10-11T08:09:43.872Z',
  //           updateTime: '0001-01-01T00:00:00Z',
  //           createBy: '',
  //           updateBy: '',
  //           platform: '',
  //           tenant: 'CCB',
  //         },
  //       },
  //       {
  //         metaDataResult: 'test',
  //         finalRate: 12,
  //         finalResult: 'Failed',
  //         manualResult: '',
  //         comment: '',
  //         createTime: '2024-10-14T03:31:48.961Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //         evaluationId: '4f93500a-f382-4492-a5c8-db9b092d6339',
  //         extractionResult: 'test',
  //         similarityResult: 'test',
  //         standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         transcriptMasterId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         nplResultId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         standardScript: {
  //           standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //           formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //           stepId: '2',
  //           scenarioId: '2_2',
  //           content:
  //             'Request the customer to provide the last four digits of their ID number.',
  //           helpContent: '',
  //           participant: 'Agent',
  //           doSimilarity: true,
  //           doExtraction: true,
  //           createTime: '2024-10-11T08:09:43.872Z',
  //           updateTime: '0001-01-01T00:00:00Z',
  //           createBy: '',
  //           updateBy: '',
  //           platform: '',
  //           tenant: 'CCB',
  //         },
  //       },
  //       {
  //         metaDataResult: 'test',
  //         finalRate: 22,
  //         finalResult: 'Failed',
  //         manualResult: '',
  //         comment: '',
  //         createTime: '2024-10-14T03:31:48.961Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //         evaluationId: '4f93500a-f382-4492-a5c8-db9b092d6339',
  //         extractionResult: 'test',
  //         similarityResult: 'test',
  //         standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         transcriptMasterId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         nplResultId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         standardScript: {
  //           standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //           formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //           stepId: '2',
  //           scenarioId: '2_3',
  //           content:
  //             'Request customers to provide their date of birth and address.',
  //           helpContent: '',
  //           participant: 'Agent',
  //           doSimilarity: true,
  //           doExtraction: true,
  //           createTime: '2024-10-11T08:09:43.872Z',
  //           updateTime: '0001-01-01T00:00:00Z',
  //           createBy: '',
  //           updateBy: '',
  //           platform: '',
  //           tenant: 'CCB',
  //         },
  //       },
  //     ],
  //   },
  // });
};

export const fireGetStandScriptResult = (
  formId: string | undefined,
  basePath = ''
) => {
  if (!formId) return;
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.detail.stand_script_result}?formId=${formId}`
  );

  // return Promise.resolve({
  //   data: {
  //     data: [
  //       {
  //         standardScriptId: 'fb4359ae-9a73-4939-9917-093465f99ce7',
  //         formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //         stepId: '1',
  //         scenarioId: '1_1',
  //         content:
  //           '你好, 今日是 {dateOfRecording}, 就您 /貴公司打算認購的股票掛鈎產品開始錄音',
  //         helpContent: '',
  //         participant: 'Agent',
  //         doSimilarity: true,
  //         doExtraction: true,
  //         createTime: '0001-01-01T00:00:00Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //       },
  //       {
  //         standardScriptId: '5b965183-5cde-4a8d-b57a-b5f7d15385f4',
  //         formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //         stepId: '2',
  //         scenarioId: '2_1',
  //         content: '{confirmation_1}',
  //         helpContent: '',
  //         participant: 'Customer',
  //         doSimilarity: false,
  //         doExtraction: false,
  //         createTime: '0001-01-01T00:00:00Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //       },
  //       {
  //         standardScriptId: '62eedb45-35c8-468c-a986-06d48b6fc62f',
  //         formId: '58ab9009-966e-4de6-b98f-93d6267d8687',
  //         stepId: '2',
  //         scenarioId: '2_2',
  //         content:
  //           '我叫{customerName}、身份證號碼係{hkid}, {dateOfBirth}，{accountNo}，{phoneNo}，{address}，{cardNo}，{accountOwner}，{onlineBankLoginID}',
  //         helpContent: '',
  //         participant: 'Customer',
  //         doSimilarity: false,
  //         doExtraction: false,
  //         createTime: '0001-01-01T00:00:00Z',
  //         updateTime: '0001-01-01T00:00:00Z',
  //         createBy: '',
  //         updateBy: '',
  //         platform: '',
  //         tenant: 'CCB',
  //       },
  //     ],
  //   },
  // });
};

export const fireDownloadReport = (
  evaluationId: string,
  recordingId: string,
  basePath = '',
  callback?: () => void
) =>
  axiosDownloadInstance({
    method: 'POST',
    url: basePath + apiConfig.paths.detail.download_nlp_report,
    responseType: 'blob',
    data: {
      evaluationId,
      recordingId,
    },
  })
    .then((response) => {
      // 从响应头中提取 Content-Disposition
      const contentDisposition = response.headers['content-disposition'];
      let fileName = 'default-filename.zip'; // 默认文件名

      // 使用正则表达式提取文件名
      if (contentDisposition) {
        const matches = contentDisposition.match(/filename=([^;]+)/);
        if (matches && matches[1]) {
          fileName = matches[1].trim(); // 确保去除多余的空格
          // fileName = fileName.replace(/^"(.+(?="$))"$/, '$1'); // 去掉文件名两端的引号
        }
      }
      // 创建一个 Blob URL 并触发下载
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName); // 使用从响应头中提取的文件名
      document.body.appendChild(link);
      link.click();
      link.remove(); // 下载完成后移除 link 元素
      console.info('文件下载成功！文件名为：', fileName);
    })
    .catch((error) => {
      console.error('文件下载失败：', error);
    })
    .finally(() => {
      callback && callback();
    });

export const fireGetWordsDetectionResult = (
  evaluationId: string | null | undefined,
  currentRecordingId: string | null | undefined,
  basePath = ''
) => {
  if (!evaluationId) return;
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.detail.words_detection_result}?evaluationId=${evaluationId}&recordingId=${currentRecordingId}`
  );
};

export const fireGetMetaData = (
  data: {
    recordingId: string;
    page: number;
    pageSize: number;
  },
  basePath = ''
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.detail.meta_data_mapping}`,
    data
  );
};

export default axiosInstance;
