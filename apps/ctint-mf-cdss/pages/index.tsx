import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TglobalConfig,
} from '@cdss-modules/design-system';
import dynamic from 'next/dynamic';
import MainLayout from '../components/_ui/MainLayout';
import { basePath, mfName } from '../lib/appConfig';
import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';
import UIBuilder from '../components/_screen/UIBuilder';
import UIBuilderPreview from '../components/_screen/UIBuilder/preview';
import DemoToolBar from '../components/_screen/DemoToolbar';
import DemoQMAdmin from '../components/_screen/DemoQMAdmin';
import AdminUserScreen from '../components/_screen/Admn/_screen/Main';
import AdminAuditScreen from '../components/_screen/Admn/_screen/Audit';
import { CommonPermissionWrapper } from '@cdss-modules/design-system/components/_other/PermissionWrapper/CommonPermissionWrap<PERSON>';
import { <PERSON>Permission } from '@cdss-modules/design-system/@types/CommonPermission';
import QMAdminScreen from '@cdss/components/_screen/Admn/_screen/QM';
import AgentPage from '../components/_screen/AgentPage';
import { useEffect } from 'react';

// @ts-expect-error: Can't type check dynamic imports
const AnnouncerModule = dynamic(() => import('announcer/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const AnnouncerModuleDetail = dynamic(() => import('announcer/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;

// @ts-expect-error: Can't type check dynamic imports
const PlaybackModule = dynamic(() => import('interaction/module'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const PlaybackModuleDetail = dynamic(() => import('interaction/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
const CallDetail = dynamic(
  () => import('../components/_screen/AgentPage/_screen/Detail/CallDetail'),
  {
    ssr: false,
    loading: () => <LoadingBlock />,
  }
) as any;
// // @ts-expect-error: Can't type check dynamic imports
// const TTSModule = dynamic(() => import('tts/module'), {
//     ssr: false,
//     loading: () => <LoadingBlock />
// }) as any;

// // @ts-expect-error: Can't type check dynamic imports
// const MsgModule = dynamic(() => import('wap/module'), {
//     ssr: false,
//     loading: () => <LoadingBlock />
// }) as any;

// @ts-expect-error: Can't type check dynamic imports
const UserAdminModule = dynamic(() => import('userAdmin/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const UserAdminDetail = dynamic(() => import('userAdmin/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;

// @ts-expect-error: Can't type check dynamic imports
const TemplateModule = dynamic(() => import('template/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const TemplateDetail = dynamic(() => import('template/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const MsgModule = dynamic(() => import('msg/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const MsgDetail = dynamic(() => import('msg/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const Info = dynamic(() => import('info/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;

const StyleGuide = dynamic(() => import('../components/_screen/StyleGuide'), {
  ssr: false,
  loading: () => <LoadingBlock />,
});
const ToolBar = dynamic(() => import('../components/_screen/ToolBar'), {
  ssr: false,
  loading: () => <LoadingBlock />,
});

// @ts-expect-error: Can't type check dynamic imports
const ReportMain = dynamic(() => import('report/module'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;

export const Page = (props: any) => {
  useEffect(() => {
    // TODO: 通过配置决定是否需要登出, 目前是针对CCB,刷新和關閉頁面都會登出
    console.log(
      'Page ==> logoutOnRefresh',
      props.globalConfig.microfrontends.logoutOnRefresh
    );
    if (props.globalConfig.microfrontends.logoutOnRefresh) {
      const handleBeforeUnload = async () => {
        localStorage.removeItem('cdss-auth-token');
        localStorage.removeItem('gc-access-token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userName');
      };
      window.addEventListener('beforeunload', handleBeforeUnload);
      window.addEventListener('unload', handleBeforeUnload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
        window.removeEventListener('unload', handleBeforeUnload);
      };
    }
  }, []);

  return (
    <PageRenderer
      routes={[
        {
          path: '/',
          group: 'ctint-mf-interaction',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled(
                    'ctint-mf-interaction',
                    'application',
                    'visit'
                  );
                }}
              >
                <PlaybackModule />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
          subroutes: [
            {
              path: '/detail',
              component: (
                <AuthLoginChecker>
                  <CommonPermissionWrapper
                    customerPermissionHandler={(
                      globalConfig: TglobalConfig,
                      permissions: string[]
                    ) => {
                      return new CommonPermission(
                        globalConfig,
                        permissions
                      ).isPermissionEnabled(
                        'ctint-mf-interaction',
                        'detail',
                        'visit'
                      );
                    }}
                  >
                    <PlaybackModuleDetail />
                  </CommonPermissionWrapper>
                </AuthLoginChecker>
              ),
            },
          ],
        },

        {
          path: '/call-patch',
          group: 'ctint-mf-agent',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled('ctint-mf-agent', 'listing', 'visit');
                }}
              >
                <AgentPage />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
          subroutes: [
            {
              path: '/detail',
              component: (
                <AuthLoginChecker>
                  <CommonPermissionWrapper
                    customerPermissionHandler={(
                      globalConfig: TglobalConfig,
                      permissions: string[]
                    ) => {
                      return new CommonPermission(
                        globalConfig,
                        permissions
                      ).isPermissionEnabled(
                        'ctint-mf-agent',
                        'detail',
                        'visit'
                      );
                    }}
                  >
                    <CallDetail />
                  </CommonPermissionWrapper>
                </AuthLoginChecker>
              ),
            },
          ],
        },
        {
          path: '/toolbar',
          group: 'ctint-mf-toolbar',
          component: <ToolBar />,
        },
        {
          path: '/demo-toolbar',
          group: 'ctint-mf-toolbar',
          component: (
            <AuthLoginChecker>
              <DemoToolBar />
            </AuthLoginChecker>
          ),
        },
        {
          path: '/demo-qm-admin',
          group: 'ctint-mf-qm',
          component: <DemoQMAdmin />,
        },
        {
          path: '/admin/qm',
          group: 'ctint-mf-admin',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                    // TODO: 需要確認修改权限名称
                  ).isPermissionEnabled('ctint-mf-admin', 'qm', 'visit');
                }}
              >
                <QMAdminScreen />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        {
          path: '/admin/audit',
          group: 'ctint-mf-admin',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled('ctint-mf-admin', 'audit', 'visit');
                }}
              >
                <AdminAuditScreen />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        {
          path: '/admin/user',
          group: 'ctint-mf-admin',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled('ctint-mf-admin', 'user', 'visit');
                }}
              >
                <AdminUserScreen />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        // {
        //     path: '/ctint-mf-tts',
        //     group: 'ctint-mf-tts',
        //     component: <TTSModule />
        // },
        // {
        //     path: '/ctint-mf-wap',
        //     group: 'ctint-mf-wap',
        //     component: <MsgModule gcClientId={process.env.NEXT_PUBLIC_MAIN_GC_CLIENT_ID} gcRedirect={`${cdssUrl}/ctint-mf-wap`} />
        // },
        {
          path: '/ctint-mf-template',
          group: 'ctint-mf-template',
          component: <TemplateModule />,
          subroutes: [
            {
              path: '/detail',
              component: <TemplateDetail />,
            },
          ],
        },
        {
          path: '/ctint-mf-msg',
          group: 'ctint-mf-msg',
          component: <MsgModule />,
          subroutes: [
            {
              path: '/detail',
              component: <MsgDetail />,
            },
          ],
        },
        {
          path: '/info',
          group: 'ctint-mf-info',
          component: <Info />,
        },
        {
          path: '/style-guide',
          group: 'style-guide',
          component: <StyleGuide />,
        },
        {
          path: '/ui-builder',
          group: 'ui-builder',
          component: <UIBuilder />,
        },
        {
          path: '/ui-preview',
          group: 'ui-builder',
          component: <UIBuilderPreview />,
        },
        {
          path: '/user-admin',
          group: 'ctint-mf-user-admin',
          component: <UserAdminModule />,
          subroutes: [
            {
              path: '/detail',
              component: <UserAdminDetail />,
            },
          ],
        },
        {
          path: '/report',
          group: 'ctint-mf-report',
          component: (
            <AuthLoginChecker>
              <ReportMain />
            </AuthLoginChecker>
          ),
        },

        {
          path: '/announcer',
          group: 'ctint-mf-announcer',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled(
                    'ctint-mf-interaction',
                    'application',
                    'visit'
                  );
                }}
              >
                <div className="size-full flex flex-col gap-4">
                  <div className="h-[300px]">
                    <DemoToolBar />
                  </div>
                  <AnnouncerModule />
                </div>
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
          subroutes: [
            {
              path: '/detail',
              component: (
                <AuthLoginChecker>
                  <CommonPermissionWrapper
                    customerPermissionHandler={(
                      globalConfig: TglobalConfig,
                      permissions: string[]
                    ) => {
                      return new CommonPermission(
                        globalConfig,
                        permissions
                      ).isPermissionEnabled(
                        'ctint-mf-interaction',
                        'detail',
                        'visit'
                      );
                    }}
                  >
                    <AnnouncerModuleDetail />
                  </CommonPermissionWrapper>
                </AuthLoginChecker>
              ),
            },
          ],
        },
      ]}
      basePath={basePath}
    >
      <MainLayout>
        <PageBody basePath={basePath} />
      </MainLayout>
    </PageRenderer>
  );
};

export const getServerSideProps = async () => {
  const globalConfig = loadGlobalConfig(mfName);
  const publicEnvVars = Object.keys(process.env).reduce(
    (publicVars: any, key) => {
      if (key.startsWith('CDSS_PUBLIC_')) {
        publicVars[key] = process.env[key];
      }
      return publicVars;
    },
    {}
  ) as any;
  return {
    props: {
      globalConfig,
      publicEnvVars,
    },
  };
};

export default Page;
