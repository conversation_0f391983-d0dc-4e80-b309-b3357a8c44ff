// messageQueueStore.ts
import { create } from 'zustand';
import { sendOutboundMessage } from 'apps/ctint-mf-cdss/lib/api';

interface QueuedMessage {
  reqId: string;
  textBody: string;
  timestamp: number;
  conversationId: string;
  basePath: string;
}

interface ConversationQueueState {
  messageQueue: QueuedMessage[];
  processingQueue: boolean;
  lastProcessTime: number;
}

interface MessageQueueState {
  // 每个会话独立的队列状态
  conversationQueues: Record<string, ConversationQueueState>;

  // Actions
  addMessage: (message: Omit<QueuedMessage, 'timestamp'>) => void;
  processQueue: (conversationId: string) => void;

  // Optional: cleanup method for when conversation ends
  removeConversationQueue: (conversationId: string) => void;
}

const createDefaultQueueState = (): ConversationQueueState => ({
  messageQueue: [],
  processingQueue: false,
  lastProcessTime: 0,
});

export const useMessageQueueStore = create<MessageQueueState>((set, get) => ({
  conversationQueues: {},

  addMessage: (message) => {
    set((state) => {
      const conversationId = message.conversationId;
      const currentQueue =
        state.conversationQueues[conversationId] || createDefaultQueueState();

      return {
        conversationQueues: {
          ...state.conversationQueues,
          [conversationId]: {
            ...currentQueue,
            messageQueue: [
              ...currentQueue.messageQueue,
              {
                ...message,
                timestamp: Date.now(),
              },
            ],
          },
        },
      };
    });

    // 开始处理该会话的队列
    get().processQueue(message.conversationId);
  },

  processQueue: async (conversationId: string) => {
    const state = get();
    const queueState = state.conversationQueues[conversationId];

    if (
      !queueState ||
      queueState.processingQueue ||
      queueState.messageQueue.length === 0
    ) {
      return;
    }

    set((state) => ({
      conversationQueues: {
        ...state.conversationQueues,
        [conversationId]: {
          ...state.conversationQueues[conversationId],
          processingQueue: true,
        },
      },
    }));

    const calculateWaitTime = (
      currentTime: number,
      lastProcessTime: number
    ): number => {
      if (lastProcessTime > currentTime) {
        return lastProcessTime + 3000 - currentTime;
      }

      const timeSinceLastProcess = currentTime - lastProcessTime;
      return timeSinceLastProcess >= 3000 ? 0 : 3000 - timeSinceLastProcess;
    };

    const processNextMessage = async () => {
      const currentState = get();
      const currentQueueState = currentState.conversationQueues[conversationId];

      if (!currentQueueState || currentQueueState.messageQueue.length === 0) {
        set((state) => ({
          conversationQueues: {
            ...state.conversationQueues,
            [conversationId]: {
              ...state.conversationQueues[conversationId],
              processingQueue: false,
            },
          },
        }));
        return;
      }

      const currentMessage = currentQueueState.messageQueue[0];
      const currentTime = Date.now();
      const waitTime = calculateWaitTime(
        currentTime,
        currentQueueState.lastProcessTime
      );

      if (waitTime > 0) {
        await new Promise((resolve) => setTimeout(resolve, waitTime));
      }

      try {
        // 更新处理时间并移除消息
        set((state) => ({
          conversationQueues: {
            ...state.conversationQueues,
            [conversationId]: {
              ...state.conversationQueues[conversationId],
              lastProcessTime: Date.now(),
              messageQueue:
                state.conversationQueues[conversationId].messageQueue.slice(1),
            },
          },
        }));

        // 异步发送消息
        sendMessage(currentMessage).catch((error) => {
          console.error('Error sending message:', error);
        });

        // 检查是否需要继续处理队列
        const updatedState = get();
        const updatedQueueState =
          updatedState.conversationQueues[conversationId];

        if (updatedQueueState && updatedQueueState.messageQueue.length > 0) {
          processNextMessage();
        } else {
          set((state) => ({
            conversationQueues: {
              ...state.conversationQueues,
              [conversationId]: {
                ...state.conversationQueues[conversationId],
                processingQueue: false,
              },
            },
          }));
        }
      } catch (error) {
        console.error('Error in message processing:', error);
        set((state) => ({
          conversationQueues: {
            ...state.conversationQueues,
            [conversationId]: {
              ...state.conversationQueues[conversationId],
              processingQueue: false,
            },
          },
        }));
      }
    };

    processNextMessage();
  },

  removeConversationQueue: (conversationId: string) => {
    set((state) => {
      const { [conversationId]: _, ...remainingQueues } =
        state.conversationQueues;
      return {
        conversationQueues: remainingQueues,
      };
    });
  },
}));

// 辅助函数：发送消息
const sendMessage = async (message: QueuedMessage) => {
  const sendResponse = await sendOutboundMessage(
    message.basePath,
    message.conversationId,
    message.reqId,
    { textBody: message.textBody }
  );
  return sendResponse;
};
