import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import { ConversationItem } from '@cdss-modules/design-system/@types/Conversation';
import {
  CDSSMessage,
  MessageData,
} from '@cdss-modules/design-system/@types/Message';
import { now } from 'lodash';
import { getUatMessage } from '../lib/api';

const getMessagesByConversationIdList = async (
  conversationIds: string[]
): Promise<MessageData[]> => {
  await new Promise((resolve) => setTimeout(resolve, 500));
  // const { basePath } = useRouteHandler();
  // Convert the mock messages format to MessageData[]
  const messagesList: MessageData[] = [];
  for (const id of conversationIds) {
    try {
      const response = await getUatMessage('/ctint/mf-cdss', id);
      if (response) {
        //修改timestamp
        messagesList.push({
          ...response.data.data,
          messages: response.data.data.messages?.map((cdssMessage: any) => ({
            ...cdssMessage,
            timestamp: new Date(cdssMessage.timestamp),
          })),
        });
      }
    } catch (error) {
      console.error(
        `Error fetching messages for conversation ID ${id}:`,
        error
      );
    }
  }

  return messagesList;
};

interface ConversationState {
  conversations: ConversationItem[];
  redothints: string[];
  messages: MessageData[];
  currentConversationId: string | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  setConversations: (conversations: ConversationItem[]) => void;
  setMessages: (messages: MessageData[]) => void;
  setCurrentConversationId: (id: string | null) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentActiveConversation: (conversationId: string) => void;
  setLastMessage: (conversationId: string, message: string) => void;
  // API Actions
  fetchMessagesByConversationIds: (conversationIds: string[]) => Promise<void>;
  handleSingleMessageData: (messageData: MessageData) => void;
  handleRealTimeMessage: (
    conversationId: string,
    messages: CDSSMessage[]
  ) => void;
  updateMessageStatus: (
    conversationId: string,
    reqId: string,
    status: string
  ) => void;

  addNewMessagesConversations: (conversationId: string) => void;
  readNewMessagesConversations: (deleteId: string) => void;
}

export const useConversationStore = create<ConversationState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      conversations: [],
      redothints: [],
      messages: [],
      currentConversationId: null,
      isLoading: false,
      error: null,

      addNewMessagesConversations: (conversationId: string) =>
        set((state) => {
          state.redothints = [...state.redothints, conversationId];
        }),

      readNewMessagesConversations: (deleteId: string) =>
        set((state) => ({
          redothints: state.redothints.filter(
            (conversationId: string) => conversationId !== deleteId
          ),
        })),

      setConversations: (conversations) =>
        set((state) => {
          state.conversations = conversations;
        }),

      setMessages: (messages) =>
        set((state) => {
          state.messages = messages;
        }),

      setLastMessage: (conversationId: string, message: string) =>
        set((state) => ({
          conversations: state.conversations.map(
            (item) =>
              item.id === conversationId
                ? { ...item, latestMessage: message } // 修改匹配项的属性
                : item // 保持其他项不变
          ),
        })),

      setCurrentConversationId: (id) =>
        set((state) => {
          state.currentConversationId = id;
        }),

      setIsLoading: (loading) =>
        set((state) => {
          state.isLoading = loading;
        }),

      setError: (error) =>
        set((state) => {
          state.error = error;
        }),
      setCurrentActiveConversation: (conversationId) => {
        set((state) => ({
          conversations: state.conversations.map((conv) => ({
            ...conv,
            isActive: conv.id === conversationId,
          })),
          currentConversationId: conversationId,
        }));
      },

      fetchMessagesByConversationIds: async (conversationIds) => {
        const { setIsLoading, setError, setMessages } = get();
        try {
          setIsLoading(true);
          setError(null);
          const messages =
            await getMessagesByConversationIdList(conversationIds);
          setMessages(messages);
        } catch (error) {
          setError(
            error instanceof Error
              ? error.message
              : 'Failed to fetch messages store'
          );
        } finally {
          setIsLoading(false);
        }
      },

      //可能有多条message 处理messages层面
      //先看reqId 如果存在则用此匹配 如存在则是api返回数据 如匹配则是等待send message的response的数据
      //不存在reqId 是websocket数据
      handleRealTimeMessage: (
        conversationId: string,
        realTimeMessages: CDSSMessage[]
      ) => {
        set((state) => {
          // const conversationId = realTimeMessages[0]?.conversationId || 'conv_1';
          // Find the MessageData object for this conversation
          const conversationMessages = state.messages.find(
            (m) => m.conversationId === conversationId
          );
          if (conversationMessages && conversationMessages.messages) {
            realTimeMessages.forEach((messageItem) => {
              console.log('real time messageItem:', messageItem);
              console.log('real time messages', conversationMessages.messages);
              // find with reqId for api response
              if (messageItem.reqId) {
                const existingMessageIndex =
                  conversationMessages.messages.findIndex(
                    (msg) => msg.reqId === messageItem.reqId
                  );
                if (existingMessageIndex !== -1) {
                  // Update existing message
                  conversationMessages.messages[existingMessageIndex] = {
                    ...messageItem,
                    timestamp: new Date(messageItem.timestamp),
                  };
                  // Skip to the next messageItem
                  return;
                }
              }
              const existingMessageIndex =
                conversationMessages.messages.findIndex(
                  (msg) => msg.id === messageItem.id
                );
              if (existingMessageIndex !== -1) {
                // Update existing message for status ws
                conversationMessages.messages[existingMessageIndex] =
                  messageItem;
              } else {
                // Add new message
                conversationMessages.messages.push(messageItem);
              }
            });
          } else {
            // Create new MessageData if conversation doesn't exist
            state.messages.push({
              startTime: now().toLocaleString(),
              conversationId,
              messages: realTimeMessages,
            });
          }
        });
      },
      //用于查询单个conversation下的message detail 的更新
      handleSingleMessageData: (messageData: MessageData) => {
        // console.log('handleSingleMessageData', messageData);

        // 先处理 messageData 中所有消息的 timestamp
        const processedMessageData = {
          ...messageData,
          messages:
            messageData.messages?.map((msg) => ({
              ...msg,
              timestamp: new Date(msg.timestamp),
            })) || [],
          whatsappConversationWindow: messageData.whatsappConversationWindow
            ? {
                ...messageData.whatsappConversationWindow,
                serviceWindowStartTime: new Date(
                  messageData.whatsappConversationWindow.serviceWindowStartTime
                ),
              }
            : undefined,
        };

        set((state) => {
          const messageIndex = state.messages.findIndex(
            (msg) => msg.conversationId === messageData.conversationId
          );
          if (messageIndex !== -1) {
            // 找到匹配的消息，创建新的数组并替换对应位置的消息
            const newMessages: MessageData[] = [...state.messages];
            newMessages[messageIndex] = processedMessageData as MessageData;
            return { messages: newMessages };
          } else {
            // 没找到匹配的消息，添加到数组末尾
            return { messages: [...state.messages, processedMessageData] };
          }
        });
      },
      updateMessageStatus: (conversationId, reqId, status) => {
        console.log('updateMessageStatus', conversationId, reqId, status);
        set((state) => {
          const conversationMessages = state.messages.find(
            (m) => m.conversationId === conversationId
          );
          if (conversationMessages && conversationMessages.messages) {
            const thisIndex = conversationMessages.messages.findIndex(
              (m) => m.reqId === reqId
            );
            if (thisIndex !== -1) {
              const oldMessage = conversationMessages.messages[thisIndex];
              conversationMessages.messages[thisIndex] = {
                ...oldMessage,
                status: status,
              };
            }
          }
        });
      },
    }))
  )
);
