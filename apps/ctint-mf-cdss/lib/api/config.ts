const isUAT = process.env.NEXT_PUBLIC_ENVIRONMENT !== 'dev';
const isRemoteEndpoint = isUAT;

export const apiConfig = {
  isUAT,
  paths: {
    login: isRemoteEndpoint
      ? `/api/process-api/ctint-auth/token`
      : `/api/login/ctint-auth/token`,
    checkAuthUser: isRemoteEndpoint
      ? `/api/process-api/ctint-auth/checkAuthUser`
      : `/api/login/ctint-auth/checkAuthUser`,
    callControl: '/api/process-api/ctint-call-control/conversations/calls',
    geVerifyUser: '/api/process-api/ctint-auth/verify/user',
    sendMessage:
      '/api/process-api/ctint-message/api/v1/cdss/conversations/messages/{conversationId}/messages',
    userList: '/api/process-api/ctint-user/admin/user',
    userAuditLogs: '/api/process-api/ctint-audit-log/user/auditLog',
    userCreate: '/api/process-api/ctint-user/admin/user/create',
    userUpdate: '/api/process-api/ctint-user/admin/user/update',
    getRoles: '/api/process-api/ctint-user/admin/role',
    getQueues: '/api/process-api/ctint-user/admin/usergroup',
    aboutAllGroupApi: '/api/process-api/ctint-user/admin/group', //user/role/queue/station
    qm: {
      stand_script_result: '/api/process-api/ctint-qm/sop/standardScript/list',
      evaluation_form: '/api/process-api/ctint-qm/sop/form/list',
      update_standard_script_score:
        '/api/process-api/ctint-qm/sop/standardScript/detail',
      upload_dictionary: '/api/process-api/ctint-qm/sop/dataDict/upload',
      meta_data_mapping:
        '/api/process-api/ctint-qm/sop/metadataTranslation/list',
    },
    recordings: '/api/process-api/ctint-conv/recordings',
    export: '/api/process-api/ctint-conv/interaction',
    gc_recordings: '/api/process-api/ctint-conv/interactions',
    sort: 'http://localhost:4400/ctint/mf-cdss/api/mock/ctint-conv/recordings',
    config: '/api/process-api/ctint-config/userconfig',
    transcript:
      'http://localhost:4400/ctint/mf-cdss/api/mock/ctint-conv/recordings/transcript',
    detail: {
      info: '/api/process-api/ctint-conv/interaction/detail',
      media: '/api/process-api/ctint-conv/interaction/recordings',
      transcript:
        '/api/process-api/ctint-conv/interaction/recordings/transcript',
      evaluation_form: '/api/process-api/ctint-qm/sop/form/list',
      evaluation_list: '/api/process-api/ctint-qm/inspection/evaluation/list',
      evaluation_assign: '/api/process-api/ctint-qm/inspection/evaluation',
      evaluation_nlp_result_update:
        '/api/process-api/ctint-qm/inspection/nlpResult/manualUpdate',
      evaluation_nlp_result_list:
        '/api/process-api/ctint-qm/inspection/nlpResult/detail/list',
      evaluation_result_update:
        '/api/process-api/ctint-qm/inspection/evaluation/detail',
      stand_script_result: '/api/process-api/ctint-qm/sop/standardScript/list',
      download_nlp_report:
        '/api/process-api/ctint-qm/inspection/excel/download',
    },
    completed_calls_pureconnect: '/api/process-api/ctint-ccba-api/calls',
    completed_calls_pureconnect_detail: {
      info: '/api/process-api/ctint-ccba-api/calls/detail',
      update: '/api/process-api/ctint-ccba-api/calls/detail/update',
    },
    getUatMessage:
      '/api/process-api/ctint-message/api/v1/cdss/conversations/messages/{conversationId}',
  },
};
