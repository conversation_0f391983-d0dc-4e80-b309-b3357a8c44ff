import { apiConfig } from './config';
import axios, { AxiosHeaders } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { addLoggingToAxios } from '@cdss-modules/design-system/lib/logging';
import { TAdminUserData } from '../../types/microfrontendsConfig';
import { TGroupPostData } from '../../types/group';
import { TAuditLogQueryParams } from '@cdss/types/adminAudit';

type UserConfigItems = {
  name: string;
  value: string;
};

type UserConfigProps = {
  filters?: UserConfigItems[];
  columns?: UserConfigItems[];
};

export const axiosInstance = axios.create({
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-cdss',
    previousId: 'ctint-bff-cdss',
    authorization: 'Basic U3ZL1wwweiscrQPkJ9fLtU3cNHngebsJ',
  },
});

export const axiosDownloadInstance = axios.create({
  timeout: 20000,
  headers: {
    traceId: uuidv4(),
    tenant: 'ccba',
    sourceId: 'ctint-mf-interaction',
    previousId: 'ctint-bff-cdss',
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', uuidv4());
      // localStorage.setItem(
      //   'gc-access-token',
      //   'ofROc0Va1i74x3CKxPJ-et8g9jwo08wFkRgCzCcIYDwdAEmJMCIh-HDn9Sgw_U17WxTvtwnngeOksDWN8Sbatw'
      // );
      localStorage.setItem(
        'cdss-auth-token',
        // 'EdOImMlG47o8McBVtqYyQbz7d4nte/7lO8HE+qEKFHupHznCcQMNoFOo+gYGxBtX8viFDj0Zgb6upeTMWiHvnNr09Q=='
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    const loginPlatform = localStorage.getItem('loginPlatform') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ccba';
    if (cdssAuthToken) {
      config.headers['cdss_authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (gcAccessToken) {
      config.headers['gc_authorization'] = 'Bearer ' + gcAccessToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }
    if (loginPlatform) {
      config.headers['loginPlatform'] = loginPlatform;
    }
    if (config.url?.includes('/interaction/recordings/transcript/')) {
      config.headers['requester'] = 'agent1';
    }
    return config;
  },

  (error) => {
    Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export const fireLogin = (
  username: string,
  password: string | null | undefined,
  deviceId: string,
  authCode?: string,
  basePath = ''
) => {
  const port = window.location.port ? `:${window.location.port}` : '';
  const redirectUri = window
    ? `${window?.location?.protocol}//${window?.location?.hostname}${port}${basePath}/login`
    : '';

  const queryParams = new URLSearchParams({
    grant_type: 'password',
    username,
    deviceId,
    redirectUrl: redirectUri,
    ...(authCode ? { authCode } : {}),
    ...(password ? { password } : {}),
  });

  return axiosInstance.post(`${basePath}${apiConfig.paths.login}`, {
    data: decodeURIComponent(queryParams.toString()),
  });
};

export const fireCheckAuthUser = (username: string, basePath = '') => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.checkAuthUser}`, {
    username,
  });
};

// Admin
export const GetUserList = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.userList}`);

export const GetRolesList = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getRoles}`);

export const GetQueueList = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getQueues}`);

// user-queue-group-portal
export const GetGroupList = (basePath = '', params: 'user' | 'queue') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.aboutAllGroupApi}?type=${params}`
  );

export const GetGroupDetail = (basePath = '', groupId: string) =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.aboutAllGroupApi}/${groupId}`
  );
export const AddGroup = (basePath = '', data: TGroupPostData) =>
  axiosInstance.post(
    `${basePath}${apiConfig.paths.aboutAllGroupApi}?type=${data.type}`,
    data
  );

export const UpdateGroup = (basePath = '', data: TGroupPostData) =>
  axiosInstance.post(
    `${basePath}${apiConfig.paths.aboutAllGroupApi}/${data.id}`,
    data
  );

export const CreateUser = (basePath = '', data: TAdminUserData) =>
  axiosInstance.post(`${basePath}${apiConfig.paths.userCreate}`, data);

export const UpdateUser = (basePath = '', data: TAdminUserData) =>
  axiosInstance.put(`${basePath}${apiConfig.paths.userUpdate}`, data);
export const GetUserAuditLogs = (basePath = '', data: TAuditLogQueryParams) =>
  axiosInstance.post(`${basePath}${apiConfig.paths.userAuditLogs}`, data);

// QM
export const fireGetStandScriptResult = (
  formId: string | undefined,
  basePath = ''
) => {
  if (!formId || formId.length === 0) return;
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.qm.stand_script_result}?formId=${formId}`
  );
};

export const fireGetFormOptions = (basePath = '') => {
  return axiosInstance.get(`${basePath}${apiConfig.paths.qm.evaluation_form}`);
};

export const fireUpdateStandardScriptScore = (basePath = '', data: any) => {
  return axiosInstance.put(
    `${basePath}${apiConfig.paths.qm.update_standard_script_score}`,
    data
  );
};

export const fireUploadDictionary = (basePath = '', data: FormData) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.qm.upload_dictionary}`,
    data,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
};

export const fireGetFilteredRecordings = (
  queryDatas: object | undefined,
  basePath = ''
) => {
  // fix: if the queryDatas does not contain conversationStart and conversationEnd, will not call the api
  if (
    queryDatas &&
    !('conversationStart' in queryDatas) &&
    !('conversationEnd' in queryDatas)
  )
    return;

  // call api to for GC
  return fireGCRecordingsByFilters(queryDatas, basePath);

  // if (
  //   queryParams.includes('isInbound') ||
  //   queryParams.includes('isOutbound') ||
  //   queryParams.includes('duration') ||
  //   queryParams.includes('order')
  // ) {
  //   return fireGetSortedRecordings(queryParams, basePath);
  // } else if (queryParams.includes('id')) {
  //   const urlParams = new URLSearchParams(queryParams);
  //   const id = urlParams?.get('id') ?? '';

  //   return fireGetSingleRecording(id, basePath);
  // } else {
  //   return fireGetAllRecordings(queryParams, basePath);
  // }
};

export const fireGetSortedRecordings = (queryParams: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.sort}?${queryParams}`);

export const fireGetAllRecordings = (queryParams: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.recordings}?${queryParams}`);

export const fireGetSingleRecordingStream = (
  recordingId: string,
  basePath = ''
) =>
  axiosDownloadInstance({
    method: 'GET',
    url: basePath + `${apiConfig.paths.recordings}/${recordingId}`,
    responseType: 'blob',
  });

export const fireGetSingleRecording = (id: string, basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.detail.info}/${id}`);

export const fireGetRecordingMedia = (conversationId: string, basePath = '') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.detail.media}/${conversationId}`
  );

export const fireGetTranscript = (conversationId: string, basePath = '') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.detail.transcript}/${conversationId}`
  );

export const fireExportRecordings = (conversationId: string[], basePath = '') =>
  axiosInstance.post(`${basePath}${apiConfig.paths.export}/recording/export`, {
    conversationId,
  });

/**
 *  Download recordings as a zip file.
 *
 *  @param {string[]} conversationId The list of conversation IDs.
 *  @param {string} [basePath] The base URL of the API.
 *  @param {() => void} callback The callback function to be called after the download is completed.
 */
export const downloadRecordingZipFile = (
  conversationId: string[],
  basePath = '',
  callback: () => void
) =>
  axiosDownloadInstance({
    method: 'POST',
    url: basePath + apiConfig.paths.export + `/recording/export`,
    responseType: 'blob',
    data: {
      conversationId,
    },
  })
    .then((response) => {
      // 从响应头中提取 Content-Disposition
      const contentDisposition = response.headers['content-disposition'];
      let fileName = 'default-filename.zip'; // 默认文件名

      // 使用正则表达式提取文件名
      if (contentDisposition) {
        const matches = contentDisposition.match(/filename=([^;]+)/);
        if (matches && matches[1]) {
          fileName = matches[1].trim(); // 确保去除多余的空格
        }
      }
      // 创建一个 Blob URL 并触发下载
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName); // 使用从响应头中提取的文件名
      document.body.appendChild(link);
      link.click();
      link.remove(); // 下载完成后移除 link 元素
      console.info('文件下载成功！文件名为：', fileName);
    })
    .catch((error) => {
      console.error('文件下载失败：', error);
    })
    .finally(() => {
      callback();
    });

export const fireGetRecordingTranscript = (mediaUri: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.transcript}`, {
    timeout: 300000,
  });

export const fireGetUserConfig = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.config}`);

export const fireCreateUserConfig = (data: UserConfigProps, basePath = '') =>
  axiosInstance.post(`${basePath}${apiConfig.paths.config}`, data);

export const fireUpdateUserConfig = (data: UserConfigProps, basePath = '') =>
  axiosInstance.put(`${basePath}${apiConfig.paths.config}`, data);

export const fireGCRecordingsByFilters = (data: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.gc_recordings}`,
    data
  );
};

// CCBA API: Get completed calls in pureconnect DB

export const fireGetCompletedCalls = (
  queryDatas: object | undefined,
  basePath = ''
) => {
  // fix: if the queryDatas does not contain conversationStart and conversationEnd, will not call the api
  if (
    queryDatas &&
    !('conversationStart' in queryDatas) &&
    !('conversationEnd' in queryDatas)
  )
    return;

  // call api to for GC
  return fireGetCompletedCallsPureconnect(queryDatas, basePath);
};

export const fireGetCompletedCallsPureconnect = (data: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.completed_calls_pureconnect}`,
    data
  );
};

export const fireGetSingleCompletedCallPureconnect = (
  id: string,
  basePath = ''
) =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.completed_calls_pureconnect_detail.info}/${id}`
  );

export const updateCompletedCallPureconnect = (
  id: string,
  data: any,
  basePath = ''
) =>
  axiosInstance.put(
    `${basePath}${apiConfig.paths.completed_calls_pureconnect_detail.update}/${id}`,
    data
  );

export const fireGetMetaDataMapping = (formId: string, basePath = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.qm.meta_data_mapping}?formId=${formId}`
  );
};

export const geVerifyUser = (key: string, value: string, basePath = '') => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.geVerifyUser}`, {
    key,
    value,
  });
};

export const fireUpdateWrapupCode = (
  basePath = '',
  wrapUpList: any[],
  participantId: string,
  conversationId: string,
  remark?: string,
  state?: string
) => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.callControl}`, {
    wrapUpList,
    participantId,
    conversationId,
    remark,
    state,
  });
};

export const fireSubmitWrapupCode = (
  basePath = '',
  wrapUpList: any[],
  participantId: string,
  conversationId: string,
  remark?: string,
  state?: string
) => {
  return axiosInstance.post(`${basePath}${apiConfig.paths.callControl}`, {
    wrapUpList,
    participantId,
    conversationId,
    remark,
    state,
  });
};

export const getUatMessage = (basePath = '', conversationId = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.getUatMessage.replace('{conversationId}', conversationId)}`
  );
};

export const sendOutboundMessage = (
  basePath = '',
  conversationId = '',
  reqId = '',
  payload = {}
) => {
  const headers = new AxiosHeaders(axiosInstance.defaults.headers);
  headers.set('reqId', reqId);
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.sendMessage.replace('{conversationId}', conversationId)}`,
    payload,
    { headers }
  );
};

export const fireGetAllActiveConverstaions = (
  basePath = '',
  userId = '',
  deviceId = ''
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl}?userId=${userId}&deviceId=${deviceId}`
  );
};

export const fireGetAllConversations = (
  basePath = '',
  userId = '',
  nextLink = '',
  conversationType = '',
  phoneNumber = ''
) => {
  return axiosInstance
    .get(
      `${basePath}${apiConfig.paths.callControl}?userId=${userId}&nextLink=${nextLink}&type=${conversationType}&phoneNumber=${phoneNumber}`
    )
    .then((res) => res?.data);
};

export default axiosInstance;
