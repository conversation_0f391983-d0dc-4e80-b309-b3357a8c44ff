/* eslint-disable @nx/enforce-module-boundaries */
import Avatar from '@cdss-modules/design-system/components/_ui/Avatar';
import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Pill from '@cdss-modules/design-system/components/_ui/Pill';
import { useToolbarContext } from '@cdss-modules/design-system/context/ToolbarContext';
import { cn } from '@cdss-modules/design-system/lib/utils';

import { useEffect, useState } from 'react';
import AgentStatus from './AgentStatus';
import { useInteractionContext } from '@cdss-modules/design-system/context/InteractionContext';
import { fireCallControlAction } from '@cdss-modules/design-system/lib/api';

// type TCallingProps = {
//   onChagePanelStatus: (status: PanelStatus) => void;
// };

const Calling = () => {
  const {
    callStatus,
    userData,
    workgroup,
    onChangePanelStatus,
    onChangeCallStatus,
  } = useToolbarContext();

  //   const [callStatus, setCallStatus] = useState<string>('Dialing');
  const [statusComponent, setStatusComponent] = useState<React.ReactNode>(
    <Pill
      variant={'flashing'}
      size={'mini'}
    >
      <span>{callStatus}</span>
    </Pill>
  );

  const [muteActive, setMuteActive] = useState(false);
  const [holdActive, setHoldActive] = useState(false);
  //   const [transferActive, setTransferActive] = useState(false);
  const [secondCallActive, setSecondCallActive] = useState(false);
  const [ivrActive, setIvrActive] = useState(false);
  // const { interactions, events } = useInteractionContext();

  const displayName = userData?.name && userData.name.charAt(0).toUpperCase();

  const handleMuteActive = () => {
    setMuteActive(!muteActive);
  };

  const handleHoldActive = () => {
    setHoldActive(!holdActive);
  };

  //   const handleTransferActive = () => {
  //     setTransferActive(!transferActive);
  //   };

  //   const handleSecondCallActive = () => {
  //     setSecondCallActive(!secondCallActive);
  //   };

  //   const handleIvrActive = () => {
  //     setIvrActive(!ivrActive);
  //   };

  const [converId, setConverId] = useState('');
  const [participantId, setParticipantId] = useState('');

  // useEffect(() => {
  //   if (events.length > 0) {
  //     events.find((event: any) => {
  //       // eslint-disable-next-line react-hooks/rules-of-hooks
  //       const { conversationId } = useExtractIdandStatus(event.event);
  //       setConverId(conversationId);
  //       const partId = event?.eventData?.data?.agent?.[0]?.id;
  //       setParticipantId(partId);
  //     });
  //   }
  // }, [events.length]);

  console.log('converId:', converId, 'participantId:', participantId);

  useEffect(() => {
    const timer = setTimeout(() => {
      onChangeCallStatus('In Call');
    }, 3000);

    return () => clearTimeout(timer);
  }, [onChangeCallStatus]);

  return (
    <div className="w-full h-full flex flex-col">
      <div className="w-full flex justify-between p-2 border-b border-grey-100">
        <div className="flex gap-x-6">
          <div className="flex items-center italic">
            Out: {userData && userData.phoneNumber} {workgroup}
          </div>
          <Pill
            variant={callStatus === 'Dialing' ? 'flashing' : 'single'}
            size={'mini'}
          >
            <span>{callStatus}</span>
          </Pill>
        </div>

        <AgentStatus />
      </div>
      <div className="w-full h-full flex items-stretch p-2">
        <div className="h-full w-[10%] flex flex-col items-center justify-center">
          <Avatar
            text={displayName as string}
            textClassName="text-body text-black"
            className="size-10"
          />
          <div className="text-remark">{userData && userData.name}</div>
          <div className="text-remark">{userData && userData.phoneNumber}</div>
        </div>

        <div className="border-l border-primary-200 w-0 mx-4" />

        <div className="flex flex-1 flex-wrap h-full gap-y-2">
          {new Array(6).fill(0).map((_, index) => (
            <div
              key={index}
              className="flex flex-col w-1/3"
            >
              <div className="font-bold">Title:</div>
              <div>Lorem ipsum</div>
            </div>
          ))}
        </div>

        <div className="border-l border-primary-200 w-0 mx-4" />

        <div className="w-1/5 grid grid-cols-3 place-items-center gap-4">
          <CallControlButton
            icon={
              <Icon
                name="mute"
                size={40}
                className={`${callStatus === 'Dialing' && 'fill-grey-200'}`}
              />
            }
            className={cn(
              'size-10',
              callStatus === 'Dialing' && 'hover:border-none'
            )}
            active={muteActive}
            handleOnChange={handleMuteActive}
            disabled={callStatus === 'Dialing'}
          />
          <CallControlButton
            icon={
              <Icon
                name="hold"
                size={20}
                className={`${callStatus === 'Dialing' && 'fill-grey-200'}`}
              />
            }
            className={cn(
              'size-10',
              callStatus === 'Dialing' && 'hover:border-none'
            )}
            active={holdActive}
            handleOnChange={() =>
              fireCallControlAction(converId, participantId, 'hold')
            }
            disabled={callStatus === 'Dialing'}
          />
          <CallControlButton
            icon={
              <Icon
                name="transfer"
                size={20}
                className={`${callStatus === 'Dialing' && 'fill-grey-200'}`}
              />
            }
            className={cn(
              'size-10',
              callStatus === 'Dialing' && 'hover:border-none'
            )}
            onClick={() => onChangePanelStatus('transfer')}
            disabled={callStatus === 'Dialing'}
          />
          <CallControlButton
            icon={
              <Icon
                name="secondCall"
                size={20}
                className={`${callStatus === 'Dialing' && 'fill-grey-200'}`}
              />
            }
            className={cn(
              'size-10',
              callStatus === 'Dialing' && 'hover:border-none'
            )}
            active={secondCallActive}
            handleOnChange={() => onChangePanelStatus('openDirectoty')}
            disabled={callStatus === 'Dialing'}
          />

          <CallControlButton
            icon={
              <Icon
                name="ivr"
                size={40}
                className={`${callStatus === 'Dialing' && 'fill-grey-200'}`}
              />
            }
            className={cn(
              'size-10',
              callStatus === 'Dialing' && 'hover:border-none'
            )}
            active={ivrActive}
            handleOnChange={() => onChangePanelStatus('ivr')}
            disabled={callStatus === 'Dialing'}
          />
        </div>

        <div className="border-l border-primary-200 w-0 mx-4" />

        <div className="flex flex-col justify-center items-center w-2/12">
          <button
            onClick={() => onChangePanelStatus('outbound')}
            className="size-8 rounded-full bg-status-danger flex items-center justify-center"
          >
            <Icon
              name="phone-end"
              className="fill-white"
            />
          </button>
          <div className="italic text-remark">00 : 01</div>
        </div>
      </div>
    </div>
  );
};

export default Calling;
