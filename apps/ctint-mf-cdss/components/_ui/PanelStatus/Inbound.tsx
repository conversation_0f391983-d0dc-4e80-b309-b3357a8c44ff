import { Panel } from '@cdss-modules/design-system';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useState } from 'react';
import { useToolbarContext } from '@cdss-modules/design-system/context/ToolbarContext';
import Avatar from '@cdss-modules/design-system/components/_ui/Avatar';
import { fireCallControlAction } from '@cdss-modules/design-system/lib/api';
import { useInteractionContext } from '@cdss-modules/design-system/context/InteractionContext';

// type TOpenFriendlistProps = {
//   title?: string;
// };

// type PersonProps = {
//   name: string;
//   phoneNumber: string;
// };

const Inbound = () => {
  const {
    onChangePanelStatus,
    // callStatus,
    // onChangeSecondCallStatus,
    // onChangeUserData,
    // onChangeSecondUserData,
  } = useToolbarContext();

  // const { interactions, events } = useInteractionContext();

  const [converId, setConverId] = useState('');
  const [participantId, setParticipantId] = useState('');

  // useEffect(() => {
  //   if (events.length > 0) {
  //     events.find((event: any) => {
  //       // eslint-disable-next-line react-hooks/rules-of-hooks
  //       const { conversationId } = useExtractIdandStatus(event.event);
  //       setConverId(conversationId);
  //       const partId = event?.eventData?.data?.agent?.[0]?.id;
  //       setParticipantId(partId);
  //     });
  //   }
  // }, [events.length]);

  console.log('converId:', converId, 'participantId:', participantId);

  return (
    <Panel
      collapsibleOpen={true}
      header={{
        title: 'Inbound',
      }}
      headerTitleSize="text-remark"
      headerClassName="rounded-t-lg animate-inbound"
      className="w-full"
      containerClassName="h-full"
    >
      <div className="flex w-full h-full p-2 items-center overflow-hidden">
        <div className="flex flex-col items-center justify-center gap-1">
          <Avatar
            text={'CA'}
            textClassName="text-body text-black"
            className="size-10"
          />
          <div className="text-remark">{'Caller Name'}</div>
          <div className="text-remark">{'98765432'}</div>
        </div>

        <div className="border-l border-primary-200 w-0 h-full mx-4" />

        <div className="flex flex-wrap h-full gap-y-2">
          {new Array(6).fill(0).map((_, index) => (
            <div
              key={index}
              className="flex flex-col w-1/3"
            >
              <div className="font-bold">Title:</div>
              <div>Lorem ipsum</div>
            </div>
          ))}
        </div>
        <div className="border-l border-primary-200 w-0 h-full mx-4" />

        <div>
          <div className="font-bold text-remark">To</div>
          <div className="border-b border-grey-100 my-2" />

          <div>
            <div>Workgroup Name</div>
            <div>91239123</div>
          </div>
        </div>
        <div className="border-l border-primary-200 w-0 h-full mx-4" />

        <div className="flex justify-between items-center w-1/5">
          <button
            onClick={() => {
              // fireCallControlAction(converId, participantId, 'pickup');
              onChangePanelStatus('calling');
            }}
            className="size-8 rounded-full bg-status-success flex items-center justify-center"
          >
            <Icon
              name="phone"
              className="fill-white"
            />
          </button>
          <button
            onClick={() => {
              fireCallControlAction(converId, participantId, 'disconnect');

              onChangePanelStatus('outbound');
            }}
            className="size-8 rounded-full bg-status-danger flex items-center justify-center"
          >
            <Icon
              name="phone-end"
              className="fill-white"
            />
          </button>
        </div>
      </div>
    </Panel>
  );
};

export default Inbound;
