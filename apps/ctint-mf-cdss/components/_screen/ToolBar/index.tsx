/* eslint-disable @nx/enforce-module-boundaries */
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from '@cdss-modules/design-system/components/_ui/Resizable';
// import useWebSocket, { ReadyState } from 'react-use-websocket';
// import { v4 as uuidv4 } from 'uuid';

import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useState } from 'react';
import { Button, Panel } from '@cdss-modules/design-system';

import Outbound from '@cdss/components/_ui/PanelStatus/Outbound';
import OpenWallboard from '@cdss/components/_ui/PanelStatus/OpenWallboard';
import OpenWorkgroup from '@cdss/components/_ui/PanelStatus/OpenWorkgroup';
import Calling from '@cdss/components/_ui/PanelStatus/Calling';
import Transfer from '@cdss/components/_ui/PanelStatus/Transfer';
import AgentStatusList from '@cdss/components/_ui/PanelStatus/AgentStatusList';
import { useToolbarContext } from '@cdss-modules/design-system/context/ToolbarContext';
// import OpenWrapup from '@cdss/components/_ui/PanelStatus/OpenWrapup';
import WrapupList from '@cdss/components/_ui/PanelStatus/WrapupList';
import OpenWrapup from '@cdss/components/_ui/PanelStatus/OpenWrapup';
import SecondCall from '@cdss/components/_ui/PanelStatus/SecondCall';
import CallingPopup from '@cdss/components/_ui/PanelStatus/CallingPopup';
import Conference from '@cdss/components/_ui/PanelStatus/Conference';
import Inbound from '@cdss/components/_ui/PanelStatus/Inbound';
import IVR from '@cdss/components/_ui/PanelStatus/IVR';
import MiniWallboard from '@cdss/components/_ui/PanelStatus/MiniWallboard';
import OpenDirectory from '@cdss/components/_ui/PanelStatus/OpenDirectory';
import { useInteractionContext } from '@cdss-modules/design-system/context/InteractionContext';

// const triggers = [
//   {
//     value: 'outbound-call',
//     label: 'Outbound Call',
//   },
//   {
//     value: 'agent-info',
//     label: 'My Info',
//   },
// ];

// const statusList = [
//   {
//     label: 'Avalible',
//     color: '#1cc500',
//   },
//   {
//     label: 'Busy',
//     color: '#ff271c',
//   },
//   {
//     label: 'Away',
//     color: '#ffe600',
//   },
//   {
//     label: 'Break',
//     color: '#0075ff',
//   },
//   {
//     label: 'Meal',
//     color: '#0075ff',
//   },
//   {
//     label: 'Meeting',
//     color: '#ff271c',
//   },
// ];

export const GridData = ({
  title,
  content,
}: {
  title: string;
  content: string;
}) => {
  return (
    <div>
      <p className="mb-1 font-bold whitespace-nowrap">{title}:</p>
      <p>{content}</p>
    </div>
  );
};

const CallPanel = ({
  show,
  area,
  bgColor,
  onClick,
}: {
  show: boolean;
  area: string;
  bgColor: string;
  onClick: () => void;
}) => {
  const [active, setActive] = useState(false);

  const handleActive = () => {
    setActive(!active);
  };

  if (!show)
    return (
      <div
        className={`size-full padding-32 text-t3 font-bold flex justify-center items-center ${bgColor}`}
      >
        <div className="flex flex-col gap-4 items-center justify-center">
          <div>Other Area {area}</div>
          <Button onClick={() => onClick()}>Put the panel here</Button>
        </div>
      </div>
    );

  return (
    <div className="call-panel">
      <Panel containerClassName="h-full call-panel__info">
        <div className="call-info">
          <div className="call-info__head">
            <div className="call-info__name">
              <h2 className="font-bold text-t5">Chan Tai Man</h2>
            </div>
            <span className="call-info__phone font-bold">(Tel: 54325432)</span>
          </div>
          <div className="call-info__all-infos">
            <div className="w-full h-[1px] border border-grey-200 my-2" />
            <div className="call-info__infos">
              <div className="call-info__info">
                <h3 className="font-bold mb-[4px]">HKDI:</h3>
                <p>A12345678</p>
              </div>
              <div className="call-info__info">
                <h3 className="font-bold mb-[4px]">Mobile no:</h3>
                <p>54325432</p>
              </div>
              <div className="call-info__info">
                <h3 className="font-bold mb-[4px]">Gender:</h3>
                <p>M</p>
              </div>
              <div className="call-info__info">
                <h3 className="font-bold mb-[4px]">Email:</h3>
                <p><EMAIL></p>
              </div>
            </div>
            <div className="call-info__other-infos">
              <div className="w-full h-[1px] border border-grey-200 my-2" />
              <div className="call-info__infos ">
                {new Array(10).fill(0).map((_, index) => (
                  <div
                    key={index}
                    className="call-info__info"
                  >
                    <h3 className="font-bold mb-[4px]">Info:</h3>
                    <p>Info</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Panel>
      <Panel containerClassName="h-full">
        <div className="call-buttons">
          <div className="call-buttons__info overflow-auto">
            <h2 className="font-bold text-t5">Some more info</h2>
            <p className="mt-4">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
              enim ad minim veniam, quis nostrud exercitation ullamco laboris
              nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in
              reprehenderit in voluptate velit esse cillum dolore eu fugiat
              nulla pariatur. Excepteur sint occaecat cupidatat non proident,
              sunt in culpa qui officia deserunt mollit anim id est laborum.
            </p>
          </div>
          <div className="call-buttons__body">
            <div className="call-buttons__buttons">
              <CallControlButton
                icon={
                  <Icon
                    name="pad"
                    size={'2em'}
                  />
                }
                label={'Pad'}
                active={active}
                handleOnChange={handleActive}
              />
              <CallControlButton
                icon={
                  <Icon
                    name="mute"
                    size={'2em'}
                  />
                }
                label={'Mute'}
                active={active}
                handleOnChange={handleActive}
              />
              <CallControlButton
                icon={
                  <Icon
                    name="hold"
                    size={'1em'}
                  />
                }
                label={'Hold'}
                active={active}
                handleOnChange={handleActive}
              />
              <div className="w-full h-[1px] border border-grey-200 call-buttons__line" />
              <CallControlButton
                icon={
                  <Icon
                    name="transfer"
                    size={'1em'}
                  />
                }
                label={'Transfer'}
                active={active}
                handleOnChange={handleActive}
              />
              <CallControlButton
                icon={
                  <Icon
                    name="consult"
                    size={'1em'}
                  />
                }
                label={'Consult'}
                active={active}
                handleOnChange={handleActive}
              />
              <CallControlButton
                icon={
                  <Icon
                    name="group"
                    size={'1em'}
                  />
                }
                label={'Group'}
                active={active}
                handleOnChange={handleActive}
              />
            </div>
          </div>
        </div>
      </Panel>
    </div>
  );
};

const CallPanelDemo = () => {
  const [active, setActive] = useState('A');

  return (
    <div className="flex-1 flex w-full h-0 gap-x-3 overflow-auto">
      <ResizablePanelGroup
        autoSaveId="example"
        direction="vertical"
        className="h-full flex flex-col gap-y-3"
      >
        <ResizablePanel className="h-full">
          <ResizablePanelGroup
            autoSaveId="example"
            direction="horizontal"
            className="flex-1 flex w-full h-0 gap-x-3 overflow-auto"
          >
            <ResizablePanel>
              <CallPanel
                show={active === 'A'}
                area="A"
                bgColor="bg-red-400"
                onClick={() => {
                  setActive('A');
                }}
              />
            </ResizablePanel>
            <ResizableHandle />
            <ResizablePanel className="h-full">
              <CallPanel
                show={active === 'B'}
                area="B"
                bgColor="bg-green-400"
                onClick={() => {
                  setActive('B');
                }}
              />
            </ResizablePanel>
          </ResizablePanelGroup>
        </ResizablePanel>
        <ResizableHandle />
        <ResizablePanel className="h-full">
          <ResizablePanelGroup
            autoSaveId="example"
            direction="horizontal"
            className="flex-1 flex w-full h-0 gap-x-3 overflow-auto"
          >
            <ResizablePanel className="h-full">
              <CallPanel
                show={active === 'C'}
                area="C"
                bgColor="bg-yellow-400"
                onClick={() => {
                  setActive('C');
                }}
              />
            </ResizablePanel>
            <ResizableHandle />
            <ResizablePanel className="h-full">
              <CallPanel
                show={active === 'D'}
                area="D"
                bgColor="bg-blue-400"
                onClick={() => {
                  setActive('D');
                }}
              />
            </ResizablePanel>
          </ResizablePanelGroup>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
};

// to be used, for open different window depends on status
// export type PanelStatus =
//   | 'outbound'
//   | 'inbound'
//   | 'openWallboard'
//   | 'openWorkgrounp'
//   | 'openFriendlist'
//   | 'transfer'
//   | 'calling'
//   | 'conference'
//   | 'wrapup'
//   | 'agentStatusList'
//   | 'wrapupList';

const ToolBarDemo = () => {
  // const [status, setStatus] = useState({
  //   label: 'Avaliable',
  //   color: '#1cc500',
  // });
  const { panelStatus, onChangePanelStatus } = useToolbarContext();
  const { /*interactions,*/ events } = useInteractionContext();

  console.log('panelStatus', panelStatus);
  const [agentStatus, setAgentStatus] = useState('Avaliable');

  const onSelectAgentStatus = (status: string) => {
    setAgentStatus(status);
  };
  // const [panelStatus, setPanelStatus] = useState<PanelStatus>('outbound');

  // const [isOnQueue, setIsOnQueue] = useState(false);

  // const onQueueChange = () => {
  //   setIsOnQueue(!isOnQueue);
  // };

  // const onChagePanelStatus = (status: PanelStatus) => {
  //   setPanelStatus(status);
  // };

  // console.log('interactions', interactions);

  const panelStatusScreen: Record<string, React.ReactNode> = {
    outbound: <Outbound />,
    openWallboard: <OpenWallboard />,
    openWorkgrounp: <OpenWorkgroup />,
    openDirectoty: <OpenDirectory />,
    calling: <Calling />,
    transfer: <Transfer />,
    agentStatusList: <AgentStatusList />,
    wrapupList: <WrapupList />,
    openWrapup: <OpenWrapup />,
    secondCall: <SecondCall />,
    popup: <CallingPopup />,
    conference: <Conference />,
    inbound: <Inbound />,
    ivr: <IVR />,
  };

  // useEffect(() => {
  //   if (events.length > 0) {
  //     events.some((event: TEventProps) => {
  //       // eslint-disable-next-line react-hooks/rules-of-hooks
  //       const { status } = useExtractIdandStatus(event.event);

  //       if (status === 'alerting') {
  //         onChangePanelStatus('inbound');
  //       }
  //     });
  //   }
  // }, [events.length]);

  return (
    <div className="w-full flex gap-2">
      {panelStatus === 'conference' &&
        'incomingCall' && ( //TODO have to do later
          <div className="bg-red-300">Call Incoming</div>
        )}
      <ResizablePanel
        className="h-full"
        minSize={25}
      >
        <div className="flex gap-4 h-full overflow-auto bg-common-bg gap-y-2 text-footnote">
          <div className="w-full h-full bg-white rounded-lg overflow-hidden">
            {panelStatusScreen[panelStatus]}
          </div>
          <div className="overflow-hidden flex w-full">
            <MiniWallboard />
          </div>
          {/* <div className="flex justify-between items-center p-2 bg-white rounded-t-lg">
            <div className={cn('flex items-center gap-x-6')}>
              {panelStatus !== 'openWallboard' && (
                <>
                  <div className="font-bold">WEBCHAT</div>
                  <div className="text-status-success">KPI: 02:32/03:00</div>
                  <div className="text-status-danger">SLA: 3s 40/3s 30</div>
                  <div>Inbound: 112</div>
                  <div>Outbound: 21</div>
                  <button className="flex items-center text-tertiary">
                    <Icon
                      name="pin"
                      size="1em"
                    />
                  </button>{' '}
                </>
              )}
            </div>

            <button
              onClick={() => {
                if (panelStatus !== 'openWallboard') {
                  onChangePanelStatus('openWallboard');
                } else {
                  onChangePanelStatus('outbound');
                }
              }}
            >
              <Icon
                name="arrow-left"
                className={cn(
                  'transition fill-tertiary-500',
                  panelStatus === 'openWallboard' ? '-rotate-90' : 'rotate-90'
                )}
              />
            </button>
          </div> */}
        </div>
      </ResizablePanel>
    </div>
  );
};

export const SocketDemo = () => {
  const { isConnected, sendMessage, lastMessage, readyState } =
    useInteractionContext();

  return (
    <div className="p-4 flex gap-4 items-center padding-2 bg-white rounded-lg">
      <h2 className="font-bold">SOCKET:</h2>
      <p>isConnected: {`${isConnected}`}</p>
      <p>readyState: {readyState}</p>
      <p>lastMessage: {`${lastMessage?.data}`}</p>
    </div>
  );
};

export const ToolBar = () => {
  // State variables to store the entire message for each type
  const [screenPopMessage, setScreenPopMessage] = useState<any>(undefined);
  const [processCallLogMessage, setProcessCallLogMessage] =
    useState<any>(undefined);
  const [openCallLogMessage, setOpenCallLogMessage] = useState<any>(undefined);
  const [interactionSubscriptionMessage, setInteractionSubscriptionMessage] =
    useState<any>(undefined);
  const [userActionSubscriptionMessage, setUserActionSubscriptionMessage] =
    useState<any>(undefined);
  const [notificationSubscriptionMessage, setNotificationSubscriptionMessage] =
    useState<any>(undefined);
  const [searchText, setSearchText] = useState<any>(undefined);

  // useEffect(() => {
  //   const handleMessage = (event: any) => {
  //     try {
  //       const message = JSON.parse(event.data);
  //       if (message) {
  //         switch (message.type) {
  //           case 'screenPop':
  //             setScreenPopMessage(event.data);
  //             break;
  //           case 'processCallLog':
  //             setProcessCallLogMessage(event.data);
  //             break;
  //           case 'openCallLog':
  //             setOpenCallLogMessage(event.data);
  //             break;
  //           case 'interactionSubscription':
  //             console.log('interactionSubscription', event.data);
  //             setInteractionSubscriptionMessage(event.data);
  //             break;
  //           case 'userActionSubscription':
  //             setUserActionSubscriptionMessage(event.data);
  //             break;
  //           case 'notificationSubscription':
  //             setNotificationSubscriptionMessage(event.data);
  //             break;
  //           case 'contactSearch':
  //             setSearchText(message.data.searchString);
  //             // sendContactSearch();
  //             break;
  //           default:
  //             break;
  //         }
  //       }
  //     } catch (error) {
  //       console.error('Error parsing message data:', error);
  //     }
  //   };

  //   window.addEventListener('message', handleMessage);

  //   return () => {
  //     window.removeEventListener('message', handleMessage);
  //   };
  // }, [searchText]);
  const doInteraction = (action: string) => {
    console.log('process interaction state change');
    const lastInteractionPayload = interactionSubscriptionMessage;
    let interactionId;
    if (lastInteractionPayload?.data?.interaction?.old) {
      interactionId = lastInteractionPayload?.data?.interaction?.old?.id;
    } else {
      interactionId = lastInteractionPayload?.data?.interaction?.id;
    }
    const payload = {
      action: action,
      id: interactionId,
    };
    const softphoneElem = document.getElementById('softphone') as any;
    softphoneElem.contentWindow.postMessage(
      JSON.stringify({
        type: 'updateInteractionState',
        data: payload,
      }),
      '*'
    );
  };
  return (
    <div className="flex flex-col h-full gap-y-4 overflow-auto">
      <ResizablePanelGroup
        direction="vertical"
        className="flex flex-col gap-y-3 h-full"
      >
        <ResizablePanel
          defaultSize={25}
          minSize={25}
          maxSize={100}
          className="toolbar-panel"
        >
          <ResizablePanelGroup
            direction="horizontal"
            className="flex-1 flex gap-4 w-full h-full"
          >
            <ToolBarDemo />
          </ResizablePanelGroup>
        </ResizablePanel>
        <ResizableHandle />
        <ResizablePanel className="flex flex-col gap-4">
          <SocketDemo />
          <div className="flex gap-4 items-center padding-2 bg-white rounded-lg">
            <Button onClick={() => doInteraction('pickup')}>pickup</Button>
            <Button onClick={() => doInteraction('disconnect')}>
              disconnect
            </Button>
            <Button onClick={() => doInteraction('hold')}>hold</Button>
            <Button onClick={() => doInteraction('mute')}>mute</Button>
            <Button onClick={() => doInteraction('securePause')}>
              securePause
            </Button>
            <div className="max-w-[200px] overflow-auto whitespace-nowrap">
              {JSON.stringify(interactionSubscriptionMessage ?? {})}
            </div>
          </div>
          <div className="h-0 flex-1 bg-red-20">
            <div className="h-full overflow-hidden">
              <div className="w-full h-full">
                {/* <iframe
              width="100%"
              height="100%"
              src="https://cdss01-jupiter.ctint.com/pages/login?redirect=%2F&accesstoken=35a451309da393b0ef1b276ddfa87ce821c5b1b4&server=https%3A%2F%2Fcdss01-jupiter.ctint.com"
            /> */}
                <div className="softphone w-full h-full bg-purple-600">
                  {/* <iframe
                    id="softphone"
                    width="100%"
                    height="100%"
                    allow="camera *; microphone *; autoplay *; hid *"
                    src="https://apps.mypurecloud.com.au/crm/embeddableFramework.html?enableFrameworkClientId=true"
                  /> */}
                </div>
              </div>
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
};

export default ToolBar;
