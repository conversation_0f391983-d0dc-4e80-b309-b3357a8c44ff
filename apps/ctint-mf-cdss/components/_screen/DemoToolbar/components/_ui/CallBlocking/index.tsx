import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, PopupContent } from '@cdss-modules/design-system';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { geVerifyUser } from '../../../../../../lib/api';
const CallBlocking = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  const [contractRules, setContractRules] = useState([
    { label: 'called many times in VIP list', checked: true },
    { label: 'exceed the call time range for VIP', checked: true },
    { label: 'reached its max.no.of promotion outcall', checked: true },
    { label: 'in call block list', checked: true },
    { label: 'in call block list and pre-reg list', checked: true },
    { label: 'in call block list and non pre-reg list', checked: true },
    {
      label: 'STOP CALL 5 Cust! Retention ONly! No Prompotion!',
      checked: true,
    },
    { label: 'RET - STOP CALL 3 Cust!', checked: true },
    { label: 'RET - STOP CALL 4 Cust!', checked: true },
    { label: 'call out in non-office hour', checked: true },
    {
      label:
        ' Not allow to call more than once a week for consented stop flag  3 customers',
      checked: true,
    },
    { label: 'Customer PPS And Contact Not Match', checked: true },
  ]);
  const verifyUser = async () => {
    await geVerifyUser(username, password);
  };
  return (
    <div>
      <Popup
        open={isOpen}
        onOpenChange={setIsOpen}
      >
        <PopupContent
          title="Call Blocking and Contact Rules Summary"
          className="h-[80vh]"
        >
          <div className="p-4  overflow-y-auto">
            <Field title="You're dialing to:">
              <Field
                title="Number:"
                direction="horizontal"
              >
                123
              </Field>
              <Field
                title="PPS:"
                direction="horizontal"
              >
                123
              </Field>
              <Field
                title="is hit PPS on call block list:"
                direction="horizontal"
              >
                123
              </Field>
            </Field>
            <Field
              title="Out Call log in current month:"
              className="py-2"
            >
              <Field
                title="=0 seconds:"
                direction="horizontal"
              >
                0
              </Field>
              <Field
                title=">15 seconds:"
                direction="horizontal"
              >
                0
              </Field>
              <Field
                title="<15 seconds:"
                direction="horizontal"
              >
                0
              </Field>
            </Field>
            <Field
              title="You're hitting below contract rules or call block:"
              className="py-2"
            >
              {contractRules.map((v, i) => (
                <Checkbox
                  disabled
                  key={i}
                  label={v.label}
                  checked={v.checked}
                />
              ))}
            </Field>
            <Field title="Supervsor Release Call Block:">
              <Field
                title="Approver Login ID:"
                className="py-2"
              >
                <Input
                  className="w-full"
                  value={username}
                  type="text"
                  size="s"
                  placeholder={'Username'}
                  onChange={(e: any) => {
                    setUsername(e);
                  }}
                />
              </Field>
              <Field
                title="Approver Password:"
                className="py-2"
              >
                <Input
                  className="w-full"
                  value={password}
                  type="text"
                  size="s"
                  placeholder={'Password'}
                  onChange={(e: any) => {
                    setPassword(e);
                  }}
                />
              </Field>
            </Field>
            <Button
              className="py-4"
              onClick={verifyUser}
            >
              OK
            </Button>
          </div>
        </PopupContent>
      </Popup>
    </div>
  );
};

export default CallBlocking;
