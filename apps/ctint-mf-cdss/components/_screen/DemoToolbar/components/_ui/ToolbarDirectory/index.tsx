import { useEffect, useMemo, useState } from 'react';
import Pill from '@cdss-modules/design-system/components/_ui/Pill';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import {
  TContact,
  TWorkgroup,
} from '@cdss/components/_screen/DemoToolbar/types';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import {
  cn,
  validateGlobalPhoneNumber,
  extractIdandStatus,
} from '@cdss-modules/design-system/lib/utils';
import { AGENT_STATUS_COLOR_MAP } from '@cdss-modules/design-system/lib/constants/status';
import {
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system/components/_ui/Tabs';
import { toast } from '@cdss-modules/design-system/components/_ui/Toast/use-toast';
import * as yup from 'yup';
import ToolbarForm from '../ToolbarForm';
import { useRole, useTabsContext } from '@cdss-modules/design-system';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import { useCallControl } from '@cdss-modules/design-system/lib/hooks/useCallControl';
import { useAttributes } from '@cdss-modules/design-system/lib/hooks/useAttributes';
import { useGetworkGroupOrUser } from '@cdss-modules/design-system/lib/hooks/useGetworkGroupOrUser';
import { TAttributesData } from '@cdss-modules/design-system/@types/attributes';
type TToolbarDirectoryProps = {
  placeholder?: string;
  selected?: any;
  onChangeInput?: (value: string) => void;
  onSelect?: (item: any) => void;
  callBy: 'id' | 'number';
};

type TOutboundPanelFilter = 'directory' | 'behalf' | 'customerInfo';
const triggers = [
  {
    value: 'user',
    label: 'User',
  },
  {
    value: 'workgroup',
    label: 'Workgroup',
  },
];

export const ToolbarDirectory = ({
  placeholder = 'Input phone no. or select a person to call',
  onChangeInput,
  selected,
  onSelect,
  callBy,
}: TToolbarDirectoryProps) => {
  const { submitAttributes, getTenantAttributesHandle } = useAttributes();
  const {
    activeParticipantData,
    updateActiveParticipantData,
    activeModal,
    closeToolbarModal,
    selectedInteraction,
    openToolbarModal,
    conversationHistoryListHandle,
    handleAfterCallFunc,
    stationContext: { station },
    eventData,
  } = useTbarContext();
  const {
    directorySearchKeyword,
    setDirectorySearchKeyword,
    setWorkgroupSearchKeyword,
    directorySearchHandler,
    getAllDirectoryHandler,
    getAllWorkgroupHandler,
    getWorkgroupByUserHandle,
    workgroupSearchHandler,
    workgroupSearchKeyword,
  } = useGetworkGroupOrUser();

  const { onChangeTab } = useTabsContext();
  const { call, consult, transfer } = useCallControl();
  const { userConfig } = useRole();
  const { tBarStatus, conversationId, agentData, consultData, customerData } =
    useHandleInteractionData(selectedInteraction);
  const customerParticipantId = customerData?.id || consultData?.id || '';
  const customerInfoYupObj = getTenantAttributesHandle.data?.reduce(
    (acc: Record<string, any>, cur: TAttributesData) => {
      if (cur?.isRequired) {
        acc[cur.key] = yup.string().required(`${[cur.name]} is required`);
      }
      return acc;
    },
    {}
  );
  const customerInfoSchema = yup.object(customerInfoYupObj).required();
  const [selectedBehalfId, setSelectedBehalfId] = useState<string>('');
  const [outboundPanelFilter, setOutboundPanelFilter] =
    useState<TOutboundPanelFilter>('directory');

  const [needSumbitAttrConvId, setNeedSumbitAttrConvId] = useState<string>('');
  const selectGroup = (person: any) => {
    onSelect && onSelect(person);
  };

  const setKeyword = (e: any | string) => {
    setDirectorySearchKeyword(e);
    setWorkgroupSearchKeyword(e);
  };
  const handleInputChange = (e: any) => {
    onChangeInput && onChangeInput(e);
    setKeyword(e);
  };

  const filteredDiretoryList = useMemo(() => {
    const directoryList = directorySearchKeyword
      ? directorySearchHandler?.data
      : getAllDirectoryHandler?.data;
    return directoryList || [];
  }, [
    directorySearchHandler?.data,
    getAllDirectoryHandler?.data,
    directorySearchKeyword,
  ]);

  const filteredWorkgroupList = useMemo(() => {
    const workgroupList = workgroupSearchKeyword
      ? workgroupSearchHandler?.data || []
      : getAllWorkgroupHandler?.data;
    return workgroupList || [];
  }, [
    workgroupSearchHandler?.data,
    getAllWorkgroupHandler?.data,
    workgroupSearchKeyword,
  ]);

  const makeCallByDifferentKey = () => {
    if (selectedBehalfId && validateGlobalPhoneNumber(directorySearchKeyword)) {
      return {
        phoneNumber: directorySearchKeyword,
        callFromQueueId: selectedBehalfId,
      };
    }

    if (selected) {
      if (selected?.presence) {
        // call user
        return {
          callUserId: selected?.id,
          phoneNumber: selected?.phoneNumber,
        };
      } else {
        // call queue
        return {
          callQueueId: selected?.id,
          phoneNumber: selected?.phoneNumber,
        };
      }
    } else {
      // call number
      if (validateGlobalPhoneNumber(directorySearchKeyword)) {
        return {
          phoneNumber: directorySearchKeyword,
        };
      }
      return {
        phoneNumber: '',
      };
    }
  };

  const makeConsultCallByDifferentKey = () => {
    if (selected) {
      if (selected?.status) {
        return {
          destinationUserId: selected?.id,
        };
      } else {
        return {
          destinationQueueId: selected?.id,
          destinationAddress: selected?.phoneNumber,
        };
      }
    } else {
      if (validateGlobalPhoneNumber(directorySearchKeyword)) {
        return { destinationAddress: directorySearchKeyword };
      }
      return {
        destinationAddress: '',
      };
    }
  };

  const handleCustomerInfoInputChange = (key: string, value: any) => {
    const isPII = getTenantAttributesHandle.data?.find(
      (item: TAttributesData) => item.key === key
    )?.isPII;

    const isEncrypted = getTenantAttributesHandle.data?.find(
      (item: TAttributesData) => item.key === key
    )?.isEncrypted;

    updateActiveParticipantData({
      ...activeParticipantData,
      [key]: {
        value: value,
        isPII,
        isEncrypted,
      },
    });
  };

  const onSubmitCustomerInfo = (
    convId?: string,
    customerOrConsultId?: string
  ) => {
    submitAttributes(
      conversationId || convId,
      customerData?.id || consultData?.id || customerOrConsultId,
      activeParticipantData
    ).then(() => conversationHistoryListHandle.refetch());
    updateActiveParticipantData(null);
  };
  useEffect(() => {
    if (needSumbitAttrConvId != '') {
      const { conversationId } = extractIdandStatus(eventData?.event) || {};
      if (conversationId == needSumbitAttrConvId) {
        const customer = eventData?.eventData?.data?.customer?.[0];
        const consults = eventData?.eventData?.data?.consult;
        const consult = consults?.[consults.length - 1];
        if (customer?.id || consult?.id) {
          onSubmitCustomerInfo(conversationId, customer?.id || consult?.id);
          setNeedSumbitAttrConvId('');
        }
      }
    }
  }, [eventData]);
  const handleDial = () => {
    if (
      selected ||
      (directorySearchKeyword &&
        validateGlobalPhoneNumber(directorySearchKeyword))
    ) {
      call(makeCallByDifferentKey()).then((res) => {
        handleAfterCallFunc(res);
        if (selectedBehalfId) {
          setNeedSumbitAttrConvId(res);
        }
      });
    } else {
      return toast({
        variant: 'error',
        title: 'Error',
        description:
          'Invalid phone number format. Please use one of the following formats: +852 6xxxxxxx, +8526xxxxxxx, or 6xxxxxxx.',
      });
    }
    closeToolbarModal();
    onSelect && onSelect(null);
    setKeyword('');
    setSelectedBehalfId('');
  };
  useEffect(() => {
    if (activeModal === 'outbound-panel' || activeModal === 'transfer-panel')
      onChangeTab('user');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeModal]);

  if (!station?.name) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="flex gap-2">
          <div className="text-status-danger text-lg">No phone selected</div>
          <button
            onClick={() => openToolbarModal('stations-panel')}
            className="flex items-center gap-x-1 "
          >
            <Icon
              name="outbound"
              className="text-primary-500"
            />
            <div>{station?.name || 'Select Phone'}</div>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex flex-col gap-y-4">
      <div className="flex gap-x-2 w-full">
        <button
          className={cn(
            'flex px-2 items-center text-xs cursor-pointer gap-x-1 whitespace-nowrap rounded-[4px] border border-grey-200',
            (outboundPanelFilter === 'directory' ||
              outboundPanelFilter === 'behalf') &&
              'bg-primary-100 border border-primary-500'
          )}
          onClick={() => {
            if (outboundPanelFilter === 'directory') {
              setOutboundPanelFilter('behalf');
            } else {
              setOutboundPanelFilter('directory');
            }
          }}
        >
          <span>
            {outboundPanelFilter === 'directory' && 'Directory'}
            {outboundPanelFilter === 'behalf' && 'Behalf'}
            {outboundPanelFilter === 'customerInfo' && 'Directory'}
          </span>
          <Icon
            name="dropdown-arrow"
            size={10}
            className=""
          />
        </button>
        <button
          className={cn(
            'flex px-2 items-center text-xs cursor-pointer gap-x-1 whitespace-nowrap rounded-[4px] border border-grey-200',
            outboundPanelFilter === 'customerInfo' &&
              'bg-primary-100 border border-primary-500'
          )}
          onClick={() => setOutboundPanelFilter('customerInfo')}
        >
          <span>Customer Information</span>
          <Icon
            name="dropdown-arrow"
            size={10}
            className=""
          />
        </button>
        <Input
          type="text"
          size="s"
          placeholder={placeholder}
          value={selected?.name ?? directorySearchKeyword}
          onChange={handleInputChange}
          allowClear
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              if (
                selected ||
                (directorySearchKeyword &&
                  validateGlobalPhoneNumber(directorySearchKeyword))
              ) {
                call(makeCallByDifferentKey()).then((res) =>
                  handleAfterCallFunc(res)
                );
              } else {
                return toast({
                  variant: 'error',
                  title: 'Error',
                  description:
                    'Invalid phone number format. Please use one of the following formats: +852 6xxxxxxx, +8526xxxxxxx, or 6xxxxxxx.',
                });
              }
              closeToolbarModal();
              setKeyword('');
            }
          }}
        />

        {callBy === 'number' && (
          <CallControlButton
            tooltip={'Dial'}
            icon={
              <Icon
                name="outbound"
                size={16}
                className="text-white"
              />
            }
            className={cn(
              'flex-none size-8 bg-status-success disabled:bg-grey-400 hover:border-none'
            )}
            onClick={handleDial}
            disabled={!directorySearchKeyword && !selected}
          />
        )}

        {callBy === 'id' && (
          <>
            <CallControlButton
              tooltip={'Consult'}
              icon={
                <Icon
                  name="outbound"
                  size={16}
                  className="text-white"
                />
              }
              className={cn(
                'flex-none size-8 bg-status-info disabled:bg-grey-400 hover:border-none'
              )}
              onClick={() => {
                consult(
                  conversationId,
                  customerParticipantId,
                  makeConsultCallByDifferentKey()
                );
                closeToolbarModal();
                onSelect && onSelect(null);
                setKeyword('');
              }}
              disabled={directorySearchKeyword === '' && !selected}
            />
            <CallControlButton
              tooltip={'Blind'}
              icon={
                <Icon
                  name="transfer"
                  size={16}
                  className="text-white"
                />
              }
              className={cn(
                'flex-none size-8 bg-status-info disabled:bg-grey-400 hover:border-none'
              )}
              onClick={() => {
                transfer(
                  conversationId,
                  agentData.id,
                  makeConsultCallByDifferentKey()
                );
                closeToolbarModal();
                onSelect && onSelect(null);
                setKeyword('');
              }}
              disabled={directorySearchKeyword === '' && !selected}
            />
          </>
        )}
      </div>
      <div className="flex-1 h-0">
        {outboundPanelFilter === 'directory' && (
          <Tabs
            defaultTab="user"
            triggers={triggers}
            triggerClassName="p-0 "
          >
            <TabsContent
              value={'user'}
              className="overflow-y-auto"
            >
              <div className="flex flex-wrap gap-2 items-stretch">
                {filteredDiretoryList.map((person: any) => {
                  return (
                    <Pill
                      variant="person"
                      key={person.id}
                      onClick={() => selectGroup(person)}
                      active={selected?.id === person.id}
                      disabled={person?.id === userConfig?.id}
                    >
                      <div className="flex items-center gap-x-4 pointer-events-auto">
                        <div className="flex flex-col items-start">
                          <div className="flex gap-1 items-center">
                            <div
                              style={{
                                background:
                                  AGENT_STATUS_COLOR_MAP[
                                    person?.presence?.presenceDefinition?.systemPresence?.toLowerCase()
                                  ],
                              }}
                              className={`size-[10px] rounded-full`}
                            />
                            <div className="text-remark">{person.name}</div>
                          </div>
                          <div className="italic text-footnote text-grey-500">
                            {person?.addresses?.[0]?.address?.slice(4)}
                          </div>
                        </div>
                      </div>
                    </Pill>
                  );
                })}
                {filteredDiretoryList.length === 0 && (
                  <div className="text-remark">
                    No matching result(s) in directory. Click the dial icon or
                    press enter to call{' '}
                    <strong>{directorySearchKeyword}</strong>.
                  </div>
                )}
              </div>
            </TabsContent>
            <TabsContent
              value={'workgroup'}
              className="overflow-y-auto"
            >
              <div className="flex flex-wrap gap-2 items-stretch">
                {filteredWorkgroupList?.map((person: any) => {
                  return (
                    <Pill
                      variant="person"
                      key={person.id}
                      onClick={() => selectGroup(person)}
                      active={selected?.id === person.id}
                    >
                      <div className="flex items-center gap-x-4 pointer-events-auto">
                        <div className="flex flex-col items-start">
                          <div className="flex gap-1 items-center">
                            <div className="size-[10px] rounded-full bg-status-success" />
                            <div className="text-remark">{person.name}</div>
                          </div>
                          <div className="italic text-footnote text-grey-500">
                            {/* {person?.addresses[0]?.address?.slice(4)} */}
                          </div>
                        </div>
                      </div>
                    </Pill>
                  );
                })}
                {filteredWorkgroupList?.length === 0 && (
                  <div className="text-remark">
                    No matching result(s) in workgroup. Click the dial icon or
                    press enter to call{' '}
                    <strong>{directorySearchKeyword}</strong>.
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        )}
        {outboundPanelFilter === 'behalf' && (
          <div className="flex flex-wrap gap-2 items-stretch">
            {getWorkgroupByUserHandle?.data?.map((queue: TWorkgroup) => {
              return (
                <Pill
                  variant="person"
                  key={queue.id}
                  onClick={() => setSelectedBehalfId(queue.id)}
                  active={selectedBehalfId === queue.id}
                >
                  <div className="flex items-center gap-x-4 pointer-events-auto">
                    <div className="flex flex-col items-start">
                      <div className="flex gap-1 items-center">
                        <div className="text-remark">{queue.name}</div>
                      </div>
                    </div>
                  </div>
                </Pill>
              );
            })}
          </div>
        )}
        {outboundPanelFilter === 'customerInfo' && (
          <ToolbarForm
            schema={customerInfoSchema}
            formItems={getTenantAttributesHandle.data}
            onSubmit={onSubmitCustomerInfo}
            handleOnChange={handleCustomerInfoInputChange}
            haveSubmitButton={tBarStatus === 'connected'}
          />
        )}
      </div>
    </div>
  );
};

export default ToolbarDirectory;
