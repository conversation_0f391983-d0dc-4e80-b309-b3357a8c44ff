import { TAttributesData } from '@cdss-modules/design-system/@types/attributes';
import {
  TMiniWallboardUser,
  TMiniWallboardQueue,
  TPresence,
  TStationContextType,
  TStatusContext,
  TWrapupContext,
} from './index';
export interface IInteractionItemProps {
  interaction: IInteractionItemResp;
  key: string;
}

export interface IInteractionItemResp {
  conversationId?: string;
  id?: string;
  conversationStart?: string;
  conversationEnd?: string;
  originatingDirection?: string;
  missedCall?: boolean;
  event?: string;
  eventData: EventData;
  isHistory?: boolean;
}
interface EventData {
  data: {
    agent: TInteractionParticipant[];
    customer?: TInteractionParticipant[];
    consult?: TInteractionParticipant[];
    ivr?: TInteractionParticipant[];
    acd?: TInteractionParticipant[];
    voice?: TInteractionParticipant[];
    workflow: TInteractionParticipant[];
  };
}
export type TInteractionParticipant = {
    address: string;
    attributes: object;
    confined: boolean;
    connectedTime?: string;
    direction: string;
    externalContact?: { id: string };
    endTime?: string;
    held: boolean;
    id: string;
    initialState: string;
    mediaRoles: string[];
    muted: boolean;
    peer?: string;
    name?: string;
    provider: string;
    purpose: string;
    recording: boolean;
    recordingState: string;
    securePause: boolean;
    state: string;
    user?: { id: string };
    wrapupRequired: boolean;
    consultParticipantId?: string;
  };

export type TContact = {
  id: string;
  name: string;
  division: {
    id: string;
    name: string;
    selfUri: string;
  };
  chat: {
    jabberId: string;
  };
  email: string;
  primaryContactInfo: TPrimaryContactInfo[];
  addresses: TAddressProps[];
  state: string;
  username: string;
  version: number;
  routingStatus: {
    status: string;
  };
  presence: TPresence;
  outOfOffice: {
    active: false;
    indefinite: false;
  };
  acdAutoAnswer: boolean;
  selfUri: string;
};

export type TPrimaryContactInfo = {
  address: string;
  mediaType: 'EMAIL' | 'PHONE';
  type: string;
};

export type TAddressProps = {
  address: string;
  display: string;
  mediaType: string;
  type: string;
  countryCode: string;
};

export type TWorkgroup = {
  id: string;
  name: string;
  divisionId: string;
  divisionName: string;
  externalPhoneNums: TIVRItem[] | null;
};

export type TIVRItem = {
  number: string;
  name: string;
  type: string;
  provider: string;
  state: string;
  formItems: TAttributesData[];
};

export type TInteractionFilter = 'current' | 'history' | 'wrapup' | null;

export type TTbarContextType = {
  eventData: any;
  conversationHistoryListHandle: any;
  handleAfterCallFunc: (convId: string) => void;
  primaryContactInfo: TPrimaryContactInfo[] | undefined;
  activeParticipantData: any | null;
  updateActiveParticipantData: (data: any) => void;
  userMiniWallboardList: TMiniWallboardUser[];
  queueMiniWallboardList: TMiniWallboardQueue[];
  selectedInteraction?: IInteractionItemResp | null;
  interactions: IInteractionItemResp[];
  activeModal?: string;
  interactionFilter: TInteractionFilter;
  openToolbarModal: (tar: string, data?: any) => void;
  closeToolbarModal: () => void;
  selectInteraction: (conversationId: IInteractionItemResp) => void;
  refreshing: boolean;
  refreshInteractions: () => void;
  updateInteractionFilter: (filter: TInteractionFilter) => void;
  wrapupContext: TWrapupContext;
  stationContext: TStationContextType;
  statusContext: TStatusContext;
  removeInteraction: (conversationId: string) => void;
};
