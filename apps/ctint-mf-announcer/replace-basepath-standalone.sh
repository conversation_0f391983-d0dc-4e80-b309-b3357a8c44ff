#!/bin/sh

# Path to the YAML configuration file.
# $APP_NAME_ENV deployment program src, will be set from k8s deployment file
# $GLOBAL_CONFIG_FILE actual deployment global config, will be set from k8s deployment file
CONFIG_FILE="/app/apps/$APP_NAME_ENV/public/config/$GLOBAL_CONFIG_FILE"

# Restore the original .next directory from the backup
rsync -a /app/orign_dist/* /app/

# get the actual host and base path
TEST_HOST=$(yq e '.microfrontends."ctint-mf-announcer".host' $CONFIG_FILE)
TEST_BASE_PATH=$(yq e '.microfrontends."ctint-mf-announcer".basepath' $CONFIG_FILE)

# check the actual host and bast path is existing
if [ -n "$TEST_HOST" ]; then
  TARGET_PATH_TEST_HOST="${TEST_HOST}"
else
  echo "TARGET_PATH_TEST_HOST is empty"
fi
if [ -n "$TEST_BASE_PATH" ]; then
  TARGET_PATH_TEST_BASE_PATH="${TEST_BASE_PATH}"
else
  echo "TARGET_PATH_TEST_BASE_PATH is empty"
fi

# replace token for host and base path
for dir in /app/apps /app/dist /app/apps/$APP_NAME_ENV/public; do
  sed -i "s|http://localhost:4040|$TARGET_PATH_TEST_HOST|g" $(find $dir -type f)
  sed -i "s|/__TEST_ANNOUNCER_BASE_PATH_TBM__|$TARGET_PATH_TEST_BASE_PATH|g" $(find $dir -type f)
done