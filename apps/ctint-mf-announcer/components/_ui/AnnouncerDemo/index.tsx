'use client';

import { useState, useEffect } from 'react';
import { Panel, Button, useRole, useRouteHandler } from '@cdss-modules/design-system';
import CDSSImage from '@cdss-modules/design-system/components/_ui/CDSSImage';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Select, TItemProps } from '@cdss-modules/design-system/components/_ui/Select';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { getAnnouncerMessageList, createFaceToFace, saveCallNature, playAnnouncerMessage, stopAnnouncerMessage, getCallNatureList,
    getConversationActiveList, getCtintCallControlStationsUsersByUserId } from 'apps/ctint-mf-announcer/lib/api';
// import { CdssUi, Button } from 'cdss-ui';

interface CallNature {
    callNatureId: number;
    callNature: string;
    active: string;
    callNatureCode: string;
}

enum MessageLanguage {
    TC = 'Cantonese',
    EN = 'English',
    SC = 'Mandarin',
}

interface AnnouncerMessage {
    MessageIdList: string;
    active: string;
    activity: string;
    announcerId: string;
    compliance: string;
    description: string;
    messageDetailCantonese: string;
    messageDetailEnglish: string;
    messageDetailMandarin: string;
    messageTypeList: string;
    product: string;
    remarks: string;
}

export type AnnouncerDemoProps = {
    testId?: string;
    titleI18n: string;
    descI18n: string;
    btnLabelI18n: string;
    onClickButton: () => void;
};
const AnnouncerDemo = ({
    testId,
    titleI18n,
    descI18n,
    btnLabelI18n,
    onClickButton,
}: AnnouncerDemoProps) => {
    const { userConfig, globalConfig } = useRole();
    const userId = userConfig?.id;

    const { basePath } = useRouteHandler();
    console.log('basePath', basePath)

    const { t, i18n } = useTranslation();
    const targetLng = i18n.language === 'en' ? 'zh-HK' : 'en';

    const languageOptions: TItemProps[] = [
        {
            id: MessageLanguage.TC,
            label: MessageLanguage.TC,
            value: MessageLanguage.TC,
        },
        {
            id: MessageLanguage.EN,
            label: MessageLanguage.EN,
            value: MessageLanguage.EN,
        },
        {
            id: MessageLanguage.SC,
            label: MessageLanguage.SC,
            value: MessageLanguage.SC,
        },
    ];

    // <select /> options
    const [activityOptions, setActivityOptions] = useState<TItemProps[]>([]);
    const [productOptions, setProductOptions] = useState<TItemProps[]>([]);
    const [complianceOptions, setComplianceOptions] = useState<TItemProps[]>([]);
    const [callNatureOptions, setCallNatureOptions] = useState<TItemProps[]>([]);

    // selected values
    const [selectedLanguage, setSelectedLanguage] = useState<string>('');
    const [selectedActivity, setSelectedActivity] = useState<string>('');
    const [selectedProduct, setSelectedProduct] = useState<string>('');
    const [selectedCompliance, setSelectedCompliance] = useState<string>('');
    const [flowResult, setFlowResult] = useState<string>(''); // annoucerId | MessageListId
    const [customerCIF, setCustomerCIF] = useState<string>('');
    const [investmentAccount, setInvestmentAccount] = useState<string>('');
    const [officerCode, setOfficerCode] = useState<string>('');
    const [callNature, setCallNature] = useState<string>('');
    const [messageDetail, setMessageDetail] = useState<string>('');

    // button disable state
    const [btnPlayDisabled, setBtnPlayDisabled] = useState<boolean>(true);
    const [btnSaveDisabled, setBtnSaveDisabled] = useState<boolean>(true);
    const [selectProductDisabled, setSelectProductDisabled] = useState<boolean>(true);
    const [selectComplianceDisabled, setSelectComplianceDisabled] = useState<boolean>(true);

    // data from api
    const [messageList, setMessageList] = useState<AnnouncerMessage[]>([]);
    const [callNatureList, setCallNatureList] = useState<CallNature[]>([]);

    // current call info
    const [currentCallId, setCurrentCallId] = useState<string>(''); // flag to indicate agent is calling.

    // temp solution: input extension 
    const [extension, setExtension] = useState<string>(''); // extension number
    const onChangeExtension = (e: any) => {
        setExtension(e);
    }

    const initialize = async () => {
        try {
            setSelectedLanguage(languageOptions[0].value); // set default language

            // call api to get data for UI display
            const getMessageListResult = await getAnnouncerMessageList(basePath);
            if (getMessageListResult.status == 200) {
                setMessageList(getMessageListResult.data.data);
            }
            const getCallNatureListResult = await getCallNatureList(basePath);
            if (getCallNatureListResult.status == 200) {
                setCallNatureList(getCallNatureListResult.data.data);
            }
        } catch (err) {
            console.log(err);
        }
    }

    useEffect(() => {
        initialize();
    }, []);

    // Render UI after init
    useEffect(() => {
        if (messageList.length) {
            const activities = [...new Set(messageList.map(data => data.activity))];
            const addActivityOptions = activities.map(activity => {
                return {
                    id: activity,
                    value: activity,
                    label: activity,
                }
            });
            console.log('addActivityOptions', addActivityOptions)
            setActivityOptions(addActivityOptions);
        }
        if (callNatureList.length) {
            const addCallNatureOptions = callNatureList.map(data => {
                return {
                    id: data.callNature,
                    value: data.callNatureCode,
                    label: data.callNature
                }
            })
            setCallNatureOptions(addCallNatureOptions);
            console.log('addCallNatureOptions', addCallNatureOptions)
        }
    }, [messageList, callNatureList]);

    const displayMessageDetail = () => {
        const displayMessage = messageList.filter(data => data.MessageIdList == flowResult);
        if (displayMessage.length) {
            if (selectedLanguage == MessageLanguage.TC) {
                setMessageDetail(displayMessage[0].messageDetailCantonese);
            } else if (selectedLanguage == MessageLanguage.EN) {
                setMessageDetail(displayMessage[0].messageDetailEnglish);
            } else if (selectedLanguage == MessageLanguage.SC) {
                setMessageDetail(displayMessage[0].messageDetailMandarin);
            }
        }
    }

    const onLanguageSelect = (e: any) => {
        setSelectedLanguage(e);
    }

    const onActivitySelect = (e: any) => {
        setSelectedActivity(e);
    }
    useEffect(() => {
        // reset product options
        setProductOptions([]);
        setSelectedProduct('');
        if (selectedActivity != '') {
            // re-render selections
            const filteredMessageList = messageList.filter(data => data.activity == selectedActivity);
            const products = [...new Set(filteredMessageList.map(data => data.product))];
            const addProductOptions = products.map(product => {
                return {
                    id: product,
                    value: product,
                    label: product,
                }
            });
            console.log('addProductOptions', addProductOptions);
            setProductOptions(addProductOptions);
            setSelectProductDisabled(false);
        }
    }, [selectedActivity]);

    const onProductSelect = (e: any) => {
        setSelectedProduct(e);
    }
    useEffect(() => {
        // reset compliance options
        setComplianceOptions([]);
        setSelectedCompliance('');
        if (selectedProduct != '') {
            // re-render selections
            const filteredMessageList = messageList.filter(data => data.product == selectedProduct);
            const compliances = [...new Set(filteredMessageList.map(data => data.compliance))];
            const addComplianceOptions = compliances.map(compliance => {
                return {
                    id: compliance,
                    value: compliance,
                    label: compliance,
                }
            });
            setComplianceOptions(addComplianceOptions);
            setSelectComplianceDisabled(false);
        }
    }, [selectedProduct]);

    const onComplianceSelect = (e: any) => {
        setSelectedCompliance(e);
    }
    useEffect(() => {
        if (selectedCompliance != '') {
            // check to assign value to display flow result
            const filteredMessageList = messageList.filter(data => data.product == selectedProduct && data.activity == selectedActivity && data.compliance == selectedCompliance);
            if (filteredMessageList.length) {
                setFlowResult(filteredMessageList[0].MessageIdList);
            }
        } else {
            setFlowResult('');
        }
    }, [selectedCompliance]);

    useEffect(() => {
        // reset
        setMessageDetail('');
        setBtnPlayDisabled(true);
        if (flowResult) {
            if (selectedActivity != '' && selectedProduct != '' && selectedCompliance != '') {
                displayMessageDetail();
                setBtnPlayDisabled(false);
            }
        }
    }, [flowResult, selectedLanguage]);

    const onChangeCustomerCIF = (e: any) => {
        setCustomerCIF(e);
    }

    const onChangeInvestmentAccount = (e: any) => {
        setInvestmentAccount(e);
    }

    const onChangeOfficerCode = (e: any) => {
        setOfficerCode(e);
    }

    const onChangeCallNature = (e: any) => {
        setCallNature(e);
        setBtnSaveDisabled(false);
    }

    const onClickSave = async () => {
        const data = {
            callId: currentCallId, // current call id
            callNature: callNature,
        }

        const response = await saveCallNature(data, basePath);
        if (response.data.data.isSuccess) {
            alert('Save successfully.');
        } else {
            alert('Save failed.');
        }
    }

    const onClickPlay = async () => {
        const data = {
            userId: userId,
            callId: currentCallId, // face to face call id
            announcerId: flowResult,
            language: selectedLanguage,
            customerCif: customerCIF,
            investmentAccount: investmentAccount,
            officerCode: officerCode,
            product: selectedProduct,
            traceId: 'ADDIN' + new Date().getTime(),
        }
        const response = await playAnnouncerMessage(data, basePath);
        console.log('on click play announcer message', response);
    }

    const onClickStop = async () => {
        const data = {
            userId: userId,
            callId: currentCallId, // face to face call id,
            traceId: 'ADDIN' + new Date().getTime(),
        }
        const response = await stopAnnouncerMessage(data, basePath);
        console.log('on click stop announcer message', response);
    }

    const onClickFaceToFace = async () => {
        const data = {
            userId: userId,
            extension: extension, // temp solution: input extension number
            traceId: 'ADDIN' + new Date().getTime(),
        }
        const response = await createFaceToFace(data, basePath);
        console.log('on click face to face', response);
        if (response.status == 200) {
            const result = response.data.data;
            setCurrentCallId(result.callId); // set the current call id
        }
    }

    // test api
    const onClickGetCurrentCallIdApiTest = async () => {
        const response = await getConversationActiveList(basePath);
        console.log('on click get current call id api test response', response);
    }

    const onClickGetExtensionApiTest = async () => {
        console.log('on click get extension api test request:', userId);
        const response = await getCtintCallControlStationsUsersByUserId(userId, basePath);
        console.log('on click get extension api test response:', response);
    }

    return (
        <Panel
            className="p-6"
            containerClassName="h-full overflow-y-auto"
        >
            <form>
                <Button
                    data-testid={`${testId}-btn`}
                    onClick={() => onClickPlay()}
                    disabled={btnPlayDisabled}
                >
                    {t('ctint-mf-announcer.addin.btnPlay')}
                </Button>
                <Button
                    data-testid={`${testId}-btn`}
                    onClick={() => onClickStop()}
                >
                    {t('ctint-mf-announcer.addin.btnStop')}
                </Button>
                <Button
                    data-testid={`${testId}-btn`}
                    onClick={() => onClickSave()}
                    disabled={btnSaveDisabled}
                >
                    {t('ctint-mf-announcer.addin.btnSave')}
                </Button>
                <Button
                    data-testid={`${testId}-btn`}
                    onClick={() => onClickFaceToFace()}
                >
                    {t('ctint-mf-announcer.addin.btnFaceToFace')}
                </Button>

                <Button
                    data-testid={`${testId}-btn`}
                    onClick={() => onClickGetCurrentCallIdApiTest()}
                >
                    {'Get Current Call ID API Test'}
                </Button>
                <Button
                    data-testid={`${testId}-btn`}
                    onClick={() => onClickGetExtensionApiTest()}
                >
                    {t('Get Extension API Test')}
                </Button>

                <div className="mb-2">
                    <label>{t('ctint-mf-announcer.addin.labelExtension')}</label>
                    <Input type="number" value={extension} onChange={onChangeExtension}></Input>
                </div>

                <div className="mb-2">
                    <label>{t('ctint-mf-announcer.addin.labelLanguage')}</label>
                    <Select
                        placeholder={t('ctint-mf-announcer.addin.labelLanguage')}
                        mode="single"
                        options={languageOptions}
                        //   showSearch={true}
                        value={selectedLanguage}
                        onChange={onLanguageSelect}
                        isPagination={false}
                    />
                </div>
                <div className='mb-2'>
                    <label>{t('ctint-mf-announcer.addin.labelActivity')}</label>
                    <Select
                        placeholder={t('ctint-mf-announcer.addin.labelActivity')}
                        mode="single"
                        options={activityOptions}
                        //   showSearch={true}
                        value={selectedActivity}
                        onChange={onActivitySelect}
                        isPagination={false}
                    />
                </div>
                <div className="mb-2">
                    <label>{t('ctint-mf-announcer.addin.labelProduct')}</label>
                    <Select
                        placeholder={t('ctint-mf-announcer.addin.labelProduct')}
                        mode="single"
                        options={productOptions}
                        //   showSearch={true}
                        value={selectedProduct}
                        onChange={onProductSelect}
                        isPagination={false}
                        disabled={selectProductDisabled}
                    />
                </div>
                <div className="mb-2">
                    <label>{t('ctint-mf-announcer.addin.labelCompliance')}</label>
                    <Select
                        placeholder={t('ctint-mf-announcer.addin.labelCompliance')}
                        mode="single"
                        options={complianceOptions}
                        //   showSearch={true}
                        value={selectedCompliance}
                        onChange={onComplianceSelect}
                        isPagination={false}
                        disabled={selectComplianceDisabled}
                    />
                </div>
                <div className="mb-2">
                    <label>{t('ctint-mf-announcer.addin.labelFlow')}</label>
                    <div className="relative flex gap-x-3 group/textarea">
                        <textarea
                            className="w-full h-[45px] border border-gray-300 rounded-md p-2 pr-8 focus:outline-none focus:border-primary-900 focus:shadow-field resize-none"
                            placeholder="Flow Result"
                            value={flowResult}
                            disabled
                        />
                    </div>
                </div>
                <div className="mb-2">
                    <label>{t('ctint-mf-announcer.addin.labelCIF')}</label>
                    <Input type="text" value={customerCIF} onChange={onChangeCustomerCIF}></Input>
                </div>
                <div className="mb-2">
                    <label>{t('ctint-mf-announcer.addin.labelInvestmentAccount')}</label>
                    <Input type="text" value={investmentAccount} onChange={onChangeInvestmentAccount}></Input>
                </div>
                <div className="mb-2">
                    <label>{t('ctint-mf-announcer.addin.labelOfficerCode')}</label>
                    <Input type="text" value={officerCode} onChange={onChangeOfficerCode}></Input>
                </div>
                <div className="mb-2">
                    <label>{t('ctint-mf-announcer.addin.labelCallNature')}</label>
                    <Select
                        placeholder={t('ctint-mf-announcer.addin.labelCallNature')}
                        mode="single"
                        options={callNatureOptions}
                        //   showSearch={true}
                        value={callNature}
                        onChange={onChangeCallNature}
                        isPagination={false}
                    />
                </div>
                <div className="mb-2">
                    <label>{t('ctint-mf-announcer.addin.labelMessageDetail')}</label>
                    <div className="relative flex gap-x-3 group/textarea">
                        <textarea
                            className="w-full h-[90px] border border-gray-300 rounded-md p-2 pr-8 focus:outline-none focus:border-primary-900 focus:shadow-field resize-none"
                            placeholder="Message Detail"
                            value={messageDetail}
                            disabled
                        />
                    </div>
                </div>
            </form>
        </Panel>
    );
}

export default AnnouncerDemo;
