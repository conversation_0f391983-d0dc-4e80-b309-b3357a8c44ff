export type TStation = {
  id: string;
  name: string;
  stationStatus?: 'AVAILABLE' | 'ASSOCIATED';
  type?: 'generic_sip' | 'inin_pcsoftphone' | 'inin_webrtc_softphone';
  lineAppearanceId?: string;
  webrtcUserId: string;
};

export type TStationData = {
  data: TStation[];
  searchResult: TStation[];
  loading: boolean;
  currentPage: number;
};

export type TStationContextType = {
  station: TStation | undefined;
  stationSearchKeyword: string;
  setStationSearchKeyword: (keyword: string) => void;
  getCurrentStation: () => void;
  stationSearchHandler: any;
  stationHandler: any;
  updateCurrentStation: (stationId: string, oldStationId?: string) => void;
  deleteCurrentStation: (stationId: string) => void;
  handleEvenStation: (formattedEventData: any) => void;
};
