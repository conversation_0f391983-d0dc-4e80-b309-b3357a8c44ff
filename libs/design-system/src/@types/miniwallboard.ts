export type TMiniWallboardUser = {
  activity: {
    inboundCalls: number;
    outboundCalls: number;
    tAlert: number;
    totalTalkTime?: number;
  };
  presence: {
    onQueue: number ;
    offQueue: number ;
  };
  userId:string;
  name:string;
};

export type TMiniWallboardQueue = {
  queueId: string;
  name: string;
  call: {
    abandonRate?: number | string;
    onQueueCount?: number | string;
    nWait?: number;
    oServiceLevel?: number;
  };
  queue: {
    oOffQueueUsers: number;
    oOnQueueUsers: number;
  };
};

export type TMiniWallboardState = 'workgroup' | 'agent';
