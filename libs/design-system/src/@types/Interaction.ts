import { TMiniWallboardQueue, TMiniWallboardUser } from "./miniwallboard";
import { TStationContextType } from "./station";
import { TPresence, TStatusContext } from "./status";
import { TWrapupContext } from "./wrapup";

export type TWrapUpFormState = {
    selectedItems: string[];
    notes: string;
    expandedItems: string[];
    itemRemarks: Array<{ itemId: string; remark: string ,wrapUpCode:string,wrapUpName:string}>;
    activeRemarkItems: string[];
  };
  
  export type TWrapUpCodeQueueData = {
    queueId: string;
    queueName: string;
    items: TWrapUpOption[];
  };
  
  export type TWrapUpOption = {
    id: string;
    parentId: string;
    type: string;
    code: string;
    name: string;
    description: string;
    tenant?: string;
    platform?: string;
    state?: string;
    items?: TWrapUpOption[];
  };
  export type TInteractionParticipant = {
    address: string;
    attributes: object;
    confined: boolean;
    connectedTime?: string;
    direction: string;
    externalContact?: { id: string };
    endTime?: string;
    held: boolean;
    id: string;
    initialState: string;
    mediaRoles: string[];
    muted: boolean;
    peer?: string;
    name?: string;
    provider: string;
    purpose: string;
    recording: boolean;
    recordingState: string;
    securePause: boolean;
    state: string;
    user?: { id: string };
    wrapupRequired: boolean;
    consultParticipantId?: string;
  };
  
  export type TEventProps = {
    event: string;
    eventData: {
      data: {
        agent: TInteractionParticipant[];
        customer: TInteractionParticipant[];
      };
      deviceId: string;
    };
  };
  export interface TAttributesData {
    key: string;
    name: string;
    type: string;
    isRequired: boolean;
    isEncrypted: boolean;
    isPII: boolean;
  }
  export type TIVRItem = {
    number: string;
    name: string;
    type: string;
    provider: string;
    state: string;
    formItems: TAttributesData[];
  };
  export type TWorkgroup = {
    id: string;
    name: string;
    divisionId: string;
    divisionName: string;
    externalPhoneNums: TIVRItem[] | null;
  };

  export type TAddressProps = {
    address: string;
    display: string;
    mediaType: string;
    type: string;
    countryCode: string;
  };
  export type TPrimaryContactInfo = {
    address: string;
    mediaType: 'EMAIL' | 'PHONE';
    type: string;
  };
  export type TContact = {
    id: string;
    name: string;
    division: {
      id: string;
      name: string;
      selfUri: string;
    };
    chat: {
      jabberId: string;
    };
    email: string;
    primaryContactInfo: TPrimaryContactInfo[];
    addresses: TAddressProps[];
    state: string;
    username: string;
    version: number;
    routingStatus: {
      status: string;
    };
    presence: TPresence;
    outOfOffice: {
      active: false;
      indefinite: false;
    };
    acdAutoAnswer: boolean;
    selfUri: string;
  };

  interface EventData {
    data: {
      agent: TInteractionParticipant[];
      customer?: TInteractionParticipant[];
      consult?: TInteractionParticipant[];
      ivr?: TInteractionParticipant[];
      acd?: TInteractionParticipant[];
      voice?: TInteractionParticipant[];
      workflow: TInteractionParticipant[];
    };
  }
  export interface IInteractionItemResp {
    conversationId?: string;
    id?: string;
    conversationStart?: string;
    conversationEnd?: string;
    originatingDirection?: string;
    missedCall?: boolean;
    event?: string;
    eventData: EventData;
    isHistory?: boolean;
  }
  export type TInteractionFilter = 'current' | 'history' | 'wrapup' | null;

  export type TTbarContextType = {
    eventData: any;
    conversationHistoryListHandle: any;
    handleAfterCallFunc: (convId: string) => void;
    primaryContactInfo: TPrimaryContactInfo[] | undefined;
    activeParticipantData: any | null;
    updateActiveParticipantData: (data: any) => void;
    userMiniWallboardList: TMiniWallboardUser[];
    queueMiniWallboardList: TMiniWallboardQueue[];
    selectedInteraction?: IInteractionItemResp | null;
    interactions: IInteractionItemResp[];
    activeModal?: string;
    interactionFilter: TInteractionFilter;
    openToolbarModal: (tar: string, data?: any) => void;
    closeToolbarModal: () => void;
    selectInteraction: (conversationId: IInteractionItemResp) => void;
    refreshing: boolean;
    refreshInteractions: () => void;
    updateInteractionFilter: (filter: TInteractionFilter) => void;
    wrapupContext: TWrapupContext;
    stationContext: TStationContextType;
    statusContext: TStatusContext;
    removeInteraction: (conversationId: string) => void;
    getSingleHistoryConversation: (v:string) => void;
    getSingleActiveConversation: (v:string) => void;
    loading: boolean;
    customerInfo?: any;
    infoLoading?: boolean;
    interactionsHistory?: any;
    historyLoading?: boolean;
    searchConversations: (value: any) => Promise<unknown>
  };