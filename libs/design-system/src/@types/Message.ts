interface MediaItem {
  id: string;
  tenant: string;
  platform: string;
  platformMediaId: string;
  host: string;
  hostType: string;
  mediaType: string;
  url: string;
  mime: string;
  filename: string;
  contentSizeBytes: number;
  createTime: Date;
  createBy: string;
  updateTime: Date;
  updateBy: string;
  scanResult: string;
}

interface MediaList {
  items: MediaItem[];
}

interface CDSSMessage {
  id: string;
  conversationId: string;
  participantId: string;
  originalPlatform: string;
  platform: string;
  platformMessageId: string;
  externalMessageId: string;
  channelId: string;
  direction: 'inbound' | 'outbound';
  messengerType: string;
  category: string;
  type: string;
  userName: string;
  userId: string;
  fromAddress: string;
  fromName: string;
  toAddress: string;
  toName: string;
  timestamp: Date;
  textBody: string;
  status: string; //queued sent delivered failed
  metadata: string;
  tenant: string;
  reqId?: string;
  mediaList?: MediaList;
  sttJobResult?: SttJobResult;
}

interface User {
  id: string;
  selfUri: string;
}

interface Queue {
  id: string;
  selfUri: string;
}

interface ConversationRoutingData {
  priority: number;
  skills?: any[];
  scoredAgents?: any[];
}

interface Address {
  name: string;
  addressNormalized: string;
  addressRaw?: string;
}

interface Participant {
  id: string;
  name: string;
  startTime: string;
  connectedTime: string;
  endTime: string;
  purpose: string;
  state: string;
  direction: string;
  disconnectType: string;
  held: boolean;
  wrapupRequired: boolean;
  wrapupPrompt: string;
  mediaRoles: string[];
  user: User;
  queue: Queue;
  attributes?: Record<string, any>;
  alertingTimeoutMs: number;
  provider: string;
  peer: string;
  conversationRoutingData: ConversationRoutingData;
  toAddress: Address;
  fromAddress: Address;
  messages?: any[];
  type: string;
}

interface Customer {
  integrationId: string;
  username: string;
  from: string;
}
interface Channel {
  id: number;
  name: string;
  integrationId: string;
  type: 'message';
  remotePlatform: 'whatsapp';
  account: string;
  remark: string;
}
interface WhatsappConversationWindow {
  serviceWindowStartTime: string;
}

interface MessageData {
  conversationId: string;
  curParticipant?: Participant;
  activeConsultParticipant?: Participant;
  customer?: Customer;
  //新增字段 日后改成 必填
  channel?: Channel;
  startTime: string;
  messages: CDSSMessage[];
  whatsappConversationWindow?: WhatsappConversationWindow;
}

interface ApiResponse {
  data: MessageData;
  isSuccess: boolean;
}
interface CreateMessagePayLoad {
  queueId: string;
  toAddress: string;
}
interface SttJobResult {
  direction: string;
  filepath: string;
  jobId: string;
  messageProvider: string;
  messageProviderAccount: string;
  providerMessageId: string;
  status: string;
  tenant: string;
  transcript: string;
}
interface InnerTranscript {
  start: string;
  end: string;
  text: string;
}

export type {
  MediaItem,
  MediaList,
  CDSSMessage,
  User,
  Queue,
  ConversationRoutingData,
  Address,
  Participant,
  Customer,
  MessageData,
  ApiResponse,
  CreateMessagePayLoad,
  WhatsappConversationWindow,
  SttJobResult,
  InnerTranscript,
};
