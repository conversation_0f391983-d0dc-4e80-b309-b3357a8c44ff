export type TPresenceDefinition = {
  id: string;
  systemPresence: string;
  selfUri: string;
};

export type TPresence = {
  source: string;
  presenceDefinition: TPresenceDefinition;
  message: string;
  modifiedDate: string;
};

export type TAgentStatusProps = {
  id: string;
  type: string;
  languageLabelEnUs: string;
  systemPresence: string;
  divisionId: string;
  deactivated: boolean;
  selfUri: string;
};

export type TStatusContext = {
  agentStatusList: TAgentStatusProps[];
  onChangeAgentStatus: (statusId: string, status: string) => void;
  onChangeIsOnQueue: () => void;
  onChangeAgentColor: (color: string) => void;
  isOnQueue: boolean;
  agentStatus: string | undefined;
  agentColor: string;
  currentStateId: string;
};
