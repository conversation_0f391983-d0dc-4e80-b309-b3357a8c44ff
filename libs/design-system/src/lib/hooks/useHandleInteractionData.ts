import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import {
  formatInteractionTiming,
  extractIdandStatus,
} from '@cdss-modules/design-system/lib/utils';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useGetworkGroupOrUser } from './useGetworkGroupOrUser';
dayjs.extend(utc);

export const useHandleInteractionData = (interaction: any) => {
  const isHistory = interaction?.isHistory;
  const { selectedInteraction } = useTbarContext();
  const loginPlatform = localStorage.getItem('loginPlatform');
  const {
    getAllDirectoryHandler: { data: directoryList },
  } = useGetworkGroupOrUser();
  let tBarStatus, conversationId;
  if (isHistory) {
    conversationId = interaction.conversationId;
    tBarStatus = '';
  } else {
    conversationId = extractIdandStatus(
      interaction?.event || ''
    )?.conversationId;
    tBarStatus = extractIdandStatus(interaction?.event || '')?.status;
  }
  const isActive =
    conversationId ==
    extractIdandStatus(selectedInteraction?.event || '')?.conversationId;

  // agent only one
  const agents = interaction?.eventData?.data?.agent;
  const agent = agents?.[agents.length - 1];

  const voicemails = interaction?.eventData?.data?.voicemail;
  const lastvoicemail = voicemails?.[voicemails.length - 1];
  const voicemail =
    lastvoicemail?.state !== 'terminated' &&
    lastvoicemail?.state !== 'disconnected'
      ? lastvoicemail
      : null;

  const allCustomers = interaction?.eventData?.data?.customer;
  const customers =
    allCustomers?.filter(
      (v: any) =>
        tBarStatus === 'disconnected' ||
        (v?.state !== 'terminated' && v?.state !== 'disconnected')
    ) || [];

  const customer = customers?.[0];

  const allconsults = interaction?.eventData?.data?.consult;
  const consults =
    allconsults?.filter(
      (v: any) =>
        tBarStatus === 'disconnected' ||
        (v?.state !== 'terminated' && v?.state !== 'disconnected')
    ) || [];
  const consult = consults?.[consults.length - 1];

  const voice = interaction?.eventData?.data?.voice?.[0];
  const workflow = interaction?.eventData?.data?.workflow?.[0];
  const acd = interaction?.eventData?.data?.acd?.[0];

  const isHaveCustomerOrConsult = Boolean(customer) || Boolean(consult);

  const consultInitiator =
    Boolean(customer) &&
    Boolean(consult) &&
    consult?.attributes?.consultInitiator
      ? 'consult'
      : 'agent';

  const allParticipants = [agent, ...customers, ...consults].filter(Boolean);

  // may transfer,consult now
  const isConference = allParticipants.length >= 3;

  const allConfenrenceParticipant = allParticipants.filter(
    (v) => !v?.confined && !v?.held
  );

  //  only confenrence
  const fullConference = allConfenrenceParticipant.length >= 3;

  const isOnlyAgent = !customer && !consult;
  const getRowData = (rowData: any, role: string) => {
    let participantName, phoneNumber;
    const isOutBound = rowData.direction == 'outbound';
    if (role === 'customer') {
      phoneNumber = isOutBound
        ? rowData?.address || rowData?.dnis
        : rowData?.address || rowData.ani;
      participantName = rowData?.name || 'N/A';
    }
    if (role === 'consult' || role === 'agent') {
      if (loginPlatform == 'pure-engage') {
        phoneNumber = isOutBound
          ? rowData?.address || rowData?.dnis
          : rowData?.address || rowData.ani;
        participantName = rowData?.name;
      } else {
        const userId = rowData?.user?.id;
        const user = directoryList?.find((item) => item.id === userId);
        phoneNumber = directoryList
          ?.find((item) => item.id === userId)
          ?.primaryContactInfo.find(
            (contact) => contact.mediaType === 'PHONE'
          )?.address;
        participantName = user?.name || 'N/A';
      }
    }
    if (role === 'acd') {
      participantName = rowData?.name || 'N/A';
      phoneNumber = 'N/A';
    }
    if (role === 'voicemail') {
      participantName = rowData?.name || 'N/A';
      phoneNumber = rowData?.dnis;
    }
    if (role === 'agent' && isOnlyAgent) {
      participantName = rowData?.name || 'N/A';
      phoneNumber =
        rowData?.dnis ||
        rowData?.errorInfo?.messageParams?.destinationAddress ||
        'unknown';
    }
    return {
      ...rowData,
      role,
      participantName,
      phoneNumber: phoneNumber?.replace(/^(tel:\+|\+)/, ''),
      telPhonneNumber: phoneNumber,
    };
  };
  const agentData = agent ? getRowData(agent, 'agent') : null;
  const acdData = acd ? getRowData(acd, 'acd') : null;

  const customerDatas = customers?.map((customer: any) => {
    return getRowData(customer, 'customer');
  });
  const customerData = customerDatas?.[0];
  const consultDatas = consults?.map((customer: any) => {
    return getRowData(customer, 'consult');
  });
  const consultData = consultDatas?.[0];

  const voicemailDatas = voicemails?.map((voicemail: any) => {
    return getRowData(voicemail, 'voicemail');
  });
  const voicemailData = voicemailDatas?.[0];
  const name =
    customerData?.participantName ||
    consultData?.participantName ||
    acdData?.participantName ||
    agentData?.participantName ||
    voicemailData?.participantName;
  const phoneNumber =
    customerData?.phoneNumber ||
    consultData?.phoneNumber ||
    acdData?.phoneNumber ||
    agentData?.phoneNumber ||
    voicemailData?.phoneNumber;
  const telPhonneNumber =
    customerData?.telPhonneNumber ||
    consultData?.telPhonneNumber ||
    acdData?.telPhonneNumber ||
    agentData?.telPhonneNumber ||
    voicemailData?.telPhonneNumber;

  return {
    connectedTime:
      customer?.connectedTime ||
      consult?.connectedTime ||
      voicemailData?.connectedTime,
    conversationId,
    isHistory,
    isActive,
    tBarStatus,
    voice,
    workflow,
    isConference,
    fullConference,
    consultInitiator,
    customerDatas,
    customerData,
    consultDatas,
    consultData,
    agentData,
    agents,
    acdData,
    voicemailData,
    name,
    phoneNumber,
    telPhonneNumber,
    isHaveCustomerOrConsult,
    talkTime: formatInteractionTiming(
      interaction?.conversationStart,
      interaction?.conversationEnd
    ),
  };
};
