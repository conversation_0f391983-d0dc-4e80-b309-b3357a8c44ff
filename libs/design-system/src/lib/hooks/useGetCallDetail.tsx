import { toast, useRoute<PERSON><PERSON><PERSON> } from "@cdss-modules/design-system";
import { fireGetSingleActiveConversation, fireGetSingleHistoryConversation } from "@cdss-modules/design-system/lib/api";
import { extractErrorMessage } from "@cdss-modules/design-system/lib/utils";
import { useState } from "react";


export const useGetCallDetail = ({ selectInteraction, setShowWrapup }: { setShowWrapup: (v: <PERSON>olean) => void, selectInteraction: (data: any) => void }) => {
    const { basePath } = useRouteHandler();
    const [loading, setLoading] = useState(false);
    async function getSingleHistoryConversation(cid: any) {
        try {
            setLoading(true)
            const res = await fireGetSingleHistoryConversation(
                cid || '',
                basePath
            );

            const data =
                res?.data?.data?.conversations != null && res.data.data?.conversations;
            if (data?.[0]?.eventData?.data?.agent?.some((agent: any) => agent.wrapupRequired)) {
                setShowWrapup(true);
            }
            selectInteraction({ ...data?.[0], isHistory: true });
            return { ...data?.[0], isHistory: true };
        } catch (error) {
            toast({
                variant: 'error',
                title: 'Error getting conversation history',
                description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
            });
        } finally {
            setLoading(false);
        }
    }
    async function getSingleActiveConversation(cid: any) {
        let result;
        try {
            if (!cid || cid == '') {
                return;
            }
            setLoading(true)
            const res = await fireGetSingleActiveConversation(cid || '', basePath);

            const data = res?.data?.data != null && res.data.data;
            if (
                data?.[0]?.eventData?.data?.agent?.some((agent: any) => agent.wrapupRequired) &&
                data?.[0]?.event?.includes('disconnected')
            ) {
                setShowWrapup(true);
            } else {
                setShowWrapup(false);
            }
            selectInteraction({ ...data?.[0], isHistory: false, isActive: true });
            result = { ...data?.[0], isHistory: false, isActive: true };
            if (data.length == 0) {
                result = getSingleHistoryConversation(cid);
                return result;
            }
            return result;
        } catch (error) {
            toast({
                variant: 'error',
                title: 'Error getting conversation history',
                description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
            });
        } finally {
              setLoading(false);
        }
    }
    return {
        getSingleActiveConversation,
        getSingleHistoryConversation,
        loading,
    }
}