import { useQuery } from '@tanstack/react-query';
import {
  fireGetAllWorkgroups,
  fireGetDirectory,
  fireGetWorkgroupsByUser,
} from '@cdss-modules/design-system/lib/api';
import { useDebounce } from '@cdss-modules/design-system/lib/hooks/useDebounce';
import { useRouteHandler } from '@cdss-modules/design-system/context/RouteContext';
import { useRole } from '@cdss-modules/design-system';
import { useState } from 'react';
import { TContact, TWorkgroup } from '@cdss-modules/design-system/@types/Interaction';

export const useGetworkGroupOrUser = () => {
  const { basePath } = useRouteHandler();
  const { userConfig } = useRole();

  const [directorySearchKeyword, setDirectorySearchKeyword] =
    useState<string>('');
  const [workgroupSearchKeyword, setWorkgroupSearchKeyword] =
    useState<string>('');
  const getWorkgroupByUserHandle = useQuery<TWorkgroup[]>({
    queryKey: ['get-workgroup-by-user'],
    queryFn: async () => {
      const res = await fireGetWorkgroupsByUser(
        basePath,
        1,
        userConfig?.id || ''
      );
      return res?.data?.data || [];
    },
  });

  const getAllDirectoryHandler = useQuery<TContact[]>({
    queryKey: ['get-all-directory'],
    queryFn: async () => {
      const res = await fireGetDirectory(basePath);
      return res?.data?.data || [];
    },
  });

  const getAllWorkgroupHandler = useQuery<TWorkgroup[]>({
    queryKey: ['get-all-workgroup'],
    queryFn: async () => {
      const res = await fireGetAllWorkgroups(basePath);
      return res?.data?.data || [];
    },
  });

  const debounceSearchKeyword = useDebounce(workgroupSearchKeyword);
  const workgroupSearchHandler = useQuery<TWorkgroup[]>({
    queryKey: ['get-workgroup-by-search', debounceSearchKeyword],
    queryFn: async () => {
      const res = await fireGetAllWorkgroups(
        basePath,
        undefined,
        debounceSearchKeyword
      );
      return res?.data?.data || [];
    },
    enabled: !!debounceSearchKeyword,
  });

  const debounceDirectorySearchKeyword = useDebounce(directorySearchKeyword);
  const directorySearchHandler = useQuery<TContact[]>({
    queryKey: ['get-directory-by-search', debounceDirectorySearchKeyword],
    queryFn: async () => {
      const res = await fireGetDirectory(
        basePath,
        undefined,
        debounceDirectorySearchKeyword
      );
      return res?.data?.data || [];
    },
    enabled: !!debounceDirectorySearchKeyword,
  });
  return {
    directorySearchKeyword,
    setDirectorySearchKeyword,
    workgroupSearchKeyword,
    setWorkgroupSearchKeyword,
    workgroupSearchHandler,
    directorySearchHandler,
    getAllDirectoryHandler,
    getAllWorkgroupHandler,
    getWorkgroupByUserHandle,
  };
};
