import { useState, useEffect } from 'react';
import { toast, useRoute<PERSON>and<PERSON> } from '@cdss-modules/design-system';
import {
  fireGetMiniWallboardUserAggregates,
  fireGetMiniWallboardQueueAggregates,
} from '../../lib/api';
import { TMiniWallboardQueue, TMiniWallboardUser } from '../../@types/miniwallboard';

export const useMiniWallBoard = () => {
  const { basePath } = useRouteHandler();
  const [queueMiniWallboardList, setQueueMiniWallboardList] = useState<
    TMiniWallboardQueue[]
  >([]);
  const [userMiniWallboardList, setUserMiniWallboardList] = useState<
    TMiniWallboardUser[]
  >([]);

  const getMiniWallboardUserAggregates = async () => {
    await fireGetMiniWallboardUserAggregates(basePath)
      .then((res: any) => {
        if (res?.data?.isSuccess) {
          setUserMiniWallboardList(res?.data?.data || {});
        }
      })
      .catch((error) => {
        return toast({
          variant: 'error',
          title: 'User State Error',
          description: `${error?.response?.data?.error}`,
        });
      });
  };

  const getMiniWallboardQueueAggregates = async () => {
    await fireGetMiniWallboardQueueAggregates(basePath)
      .then((res: any) => {
        if (res?.data?.isSuccess) {
          setQueueMiniWallboardList(res?.data?.data || []);
        }
      })
      .catch((error) => {
        return toast({
          variant: 'error',
          title: 'User Conversation Error',
          description: `${error?.response?.data?.error}`,
        });
      });
  };

  const handleEventSetQueueMiniwallBoard = (eventData: any) => {
    const newQueueMiniWallboardList = queueMiniWallboardList?.map((v) => {
      if (v?.queueId == eventData?.queueId) {
        v = { ...eventData, name: v.name };
      }
      return v;
    });
    setQueueMiniWallboardList(newQueueMiniWallboardList);
  };

  const handleEventSetUserMiniwallBoard = (eventData: any) => {
    setUserMiniWallboardList([eventData]);
  };

  const getMiniWallBoardInitiation = () => {
    getMiniWallboardQueueAggregates();
    getMiniWallboardUserAggregates();
  };
  useEffect(() => {
    getMiniWallBoardInitiation();
  }, []);
  return {
    handleEventSetUserMiniwallBoard,
    handleEventSetQueueMiniwallBoard,
    getMiniWallBoardInitiation,
    queueMiniWallboardList,
    userMiniWallboardList,
  };
};
