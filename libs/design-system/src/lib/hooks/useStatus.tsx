import { useEffect, useState } from 'react';
import {
  fireUpdateUserRoutingStatus,
  fireGetAllAgentStatus,
  fireGetCurrentUser,
} from '@cdss-modules/design-system/lib/api';
import { AGENT_STATUS_COLOR_MAP } from '@cdss-modules/design-system/lib/constants/status';
import { useRole } from '@cdss-modules/design-system';
import { useRouteHandler } from '@cdss-modules/design-system/context/RouteContext';
import { useAgentActivityStore } from '@cdss/store/agentActivityState';
import { TAgentStatusProps } from '@cdss-modules/design-system/@types/status';
import { TPrimaryContactInfo } from '@cdss-modules/design-system/@types/Interaction';

export const useStatus = () => {
  const { activity, setActivity } = useAgentActivityStore();
  const { basePath } = useRouteHandler();
  const { userConfig } = useRole();
  const [agentColor, setAgentColor] = useState('');
  const [isOnQueue, setIsOnQueue] = useState(false);
  const [agentStatus, setAgentStatus] = useState<string | undefined>('');
  const [agentStatusList, setAgentStatusList] = useState<TAgentStatusProps[]>(
    []
  );
  const [onQueueStateId, setOnQueueStateId] = useState<string>('');
  const [currentStateId, setCurrentStateId] = useState<string>('');
  const [availableStateId, setAavailableStateId] = useState<string>('');
  const [primaryContactInfo, setPrimaryContactInfo] = useState<
    TPrimaryContactInfo[] | undefined
  >([]);
  const onChangeIsOnQueue = () => {
    try {
      const id = isOnQueue
        ? currentStateId
          ? currentStateId
          : availableStateId
        : onQueueStateId;
      fireUpdateUserRoutingStatus(userConfig?.id as any, basePath, id);
    } catch (error) {
      console.error(error);
    }
  };

  const onChangeAgentColor = (color: string) => {
    setAgentColor(color);
  };

  const onChangeAgentStatus = (statusId: string, status: string) => {
    try {
      fireUpdateUserRoutingStatus(userConfig?.id as any, basePath, statusId);
      setCurrentStateId(statusId);
      setAgentStatus(status);
    } catch (error) {
      console.error(error);
    }
  };

  const getAllAgentStatus = async () => {
    try {
      fireGetAllAgentStatus(basePath).then((res) => {
        if (res?.data?.isSuccess) {
          const filteredData: TAgentStatusProps[] = [];
          res?.data?.data?.map((item: TAgentStatusProps) => {
            if (
              item.systemPresence === 'OnQueue' ||
              item.systemPresence === 'Ready'
            ) {
              setOnQueueStateId(item.id);
            }
            if (
              !['OnQueue', 'Offline', 'Idle', 'Ready'].includes(
                item.systemPresence
              )
            ) {
              filteredData.push(item);
            }
            if (
              item.systemPresence === 'Available' ||
              item.systemPresence === 'NotReady'
            ) {
              setAavailableStateId(item.id);
            }
          });
          setAgentStatusList(filteredData);
        }
      });
    } catch (error) {
      console.log(error);
    }
  };
  const getCurrentStatus = async () => {
    try {
      fireGetCurrentUser(basePath).then((res) => {
        if (res?.data?.isSuccess) {
          setPrimaryContactInfo(res?.data?.primaryContactInfo);
          const currentStatusItem = res?.data?.presence?.presenceDefinition;
          const agentCurrentStatus = currentStatusItem?.systemPresence;
          const agentCurrentStatusId = currentStatusItem?.id;
          if (['On Queue', 'Ready'].includes(agentCurrentStatus)) {
            setIsOnQueue(true);
            setAgentStatus('On Queue');
          } else if (['Offline', 'NotReady'].includes(agentCurrentStatus)) {
            setIsOnQueue(false);
            setAgentStatus('Offline');
          } else {
            const agentCurrentsubStatus = agentStatusList.find(
              (v) => v.id == agentCurrentStatusId
            )?.languageLabelEnUs;
            setIsOnQueue(false);
            setAgentStatus(agentCurrentsubStatus);
            setCurrentStateId(agentCurrentStatusId);
          }
          setAgentColor(
            AGENT_STATUS_COLOR_MAP[agentCurrentStatus?.toLowerCase()]
          );
        }
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleEvenStatus = (formattedEventData: any) => {
    // for trigger the "make elligiable for interactive popup"
    if (userConfig?.id == formattedEventData?.id) {
      const activityState = formattedEventData?.routingStatus?.status;
      setActivity(activityState);
    }
    const presenceDefinition = formattedEventData?.presence?.presenceDefinition;
    if (
      presenceDefinition?.systemPresence === 'ON_QUEUE' ||
      presenceDefinition?.systemPresence === 'Ready'
    ) {
      setIsOnQueue(true);
      setAgentStatus('On Queue');
      setAgentColor('#21c0f6');
    } else {
      const statusId = presenceDefinition?.id;
      const findStatusItem = agentStatusList.find(
        (status: TAgentStatusProps) => status.id === statusId
      ) as TAgentStatusProps;
      setCurrentStateId(statusId);
      setIsOnQueue(false);
      setAgentStatus(findStatusItem?.languageLabelEnUs);
      setAgentColor(
        AGENT_STATUS_COLOR_MAP[findStatusItem?.systemPresence?.toLowerCase()]
      );
    }
  };
  return {
    agentColor,
    isOnQueue,
    agentStatus,
    agentStatusList,
    onChangeIsOnQueue,
    onChangeAgentColor,
    onChangeAgentStatus,
    getCurrentStatus,
    handleEvenStatus,
    primaryContactInfo,
    currentStateId,
    getAllAgentStatus,
  };
};
