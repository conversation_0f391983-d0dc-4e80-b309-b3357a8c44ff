import { useState } from 'react';
import { toast } from '@cdss-modules/design-system';
import { fireGetWrapupCategory } from '@cdss-modules/design-system/lib/api';
import { useRole } from '@cdss-modules/design-system';
import { useRouteHandler } from '@cdss-modules/design-system/context/RouteContext';
import { useQuery } from '@tanstack/react-query';
export const useWrapUp = () => {
  const { basePath } = useRouteHandler();
  const { userConfig } = useRole();
  const [showWrapup, setShowWrapup] = useState<boolean>(false);
  const [selectLastActiveAgentId, setSelectLastActiveAgentId] =
    useState<string>('');
  const [selectLastActiveConvId, setSelectLastActiveConvId] =
    useState<string>('');
  const [selectedWrapupList, setSelectedWrapupList] = useState<any[]>([]);
  const [selectedWrapupId, setSelectedWrapupId] = useState<string | null>(null);
  const wrapupCategoryListHandle = useQuery({
    queryKey: ['wrapup-category-list'],
    queryFn: async () => {
      const res = await fireGetWrapupCategory(
        userConfig?.id as string,
        basePath
      );
      if (res?.data?.isSuccess) {
        return res?.data?.data;
      } else {
        toast({
          variant: 'error',
          title: 'Error',
          description: res?.data?.error,
        });
        return [];
      }
    },
  });

  return {
    showWrapup,
    setShowWrapup,
    wrapupCategoryListHandle:wrapupCategoryListHandle?.data?.[0]?.items,
    wrapupCategoryFullListHandle:wrapupCategoryListHandle?.data,
    selectLastActiveAgentId,
    setSelectLastActiveAgentId,
    selectLastActiveConvId,
    setSelectLastActiveConvId,
    selectedWrapupList,
    setSelectedWrapupList,
    selectedWrapupId,
    setSelectedWrapupId,
  };
};
