import { type ClassValue, clsx } from 'clsx';
import { extendTailwindMerge } from 'tailwind-merge';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import duration from 'dayjs/plugin/duration';

import { fireLogout } from './api';
import { GLOBAL_DATE_FORMAT } from './constants';
dayjs.extend(relativeTime);
dayjs.extend(duration);

interface StatusColorMap {
  [key: string]: string;
}

interface RoutingStatusMap {
  [key: string]: string;
}

const twMerge = extendTailwindMerge({
  extend: {
    classGroups: {
      'font-size': [
        'text-t0',
        'text-t1',
        'text-t2',
        'text-t3',
        'text-t4',
        'text-t5',
        'text-t6',
        'text-body',
        'text-remark',
        'text-footnote',
        'text-mini',
      ],
      h: ['field'],
    },
  },
});

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const replaceMdPattern = (input: string): string => {
  return input.replace(/\.md\)/g, ')');
};

export const addIdsToMarkdownHeadings = (markdown: string): string => {
  const headingRegex = /^(#+) (.*)$/gm;

  return markdown.replace(headingRegex, (match, hashes, headingText) => {
    // Slugify the heading text. You might want a more sophisticated slugify function for complex cases.
    const slug = headingText
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '-')
      // eslint-disable-next-line no-useless-escape
      .replace(/[^\w\-]+/g, '');
    // Return the modified heading with an ID in markdown format.
    // Markdown doesn't officially support IDs in the heading directly, so we need to use inline HTML.
    return `\n${hashes} ${headingText} <a id="${slug}" class="anchor-heading"></a>\n`;
  });
};

export const replaceClassWithClassName = (input: string): string => {
  return input.replace(/class="/g, 'className="');
};

export const formatMD = (input: string): string => {
  let result = input;
  result = replaceMdPattern(result);
  result = addIdsToMarkdownHeadings(result);
  result = replaceClassWithClassName(result);
  return result;
};

export const camelCaseToWords = (str: string) => {
  if (typeof str !== 'string') {
    return str;
  }
  return str
    .replace(/([a-z0-9])([A-Z])/g, '$1 $2') // insert a space before a capital letter if it's preceded by a lowercase letter or a digit
    .replace(/^./, function (str) {
      return str.toUpperCase();
    }) // uppercase the first character
    .trim(); // remove any leading or trailing spaces
};

export const formatAudioTime = (seconds: number) => {
  if (seconds === Infinity) {
    return '--';
  }
  const floored = Math.floor(seconds);
  let from = 14;
  let length = 5;
  // Display hours only if necessary.
  if (floored >= 3600) {
    from = 11;
    length = 8;
  }

  return new Date(floored * 1000).toISOString().substr(from, length);
};

export const getOptionLabelFromValue = (
  options: any[],
  value: string | number
) => {
  return options.find((option) => option?.value === value)?.label;
};

export const secondsToTimeDisplay = (
  seconds?: number,
  labels?: [string, string, string]
) => {
  if (!seconds) return '';
  const defaultLabels = ['h', 'm', 's'];
  const hrLabel = labels?.[0] ?? defaultLabels[0];
  const minLabel = labels?.[1] ?? defaultLabels[1];
  const secLabel = labels?.[2] ?? defaultLabels[2];
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  let result = '';
  if (hours > 0) {
    result += `${hours}${hrLabel} `;
  }
  if (minutes > 0) {
    result += `${minutes}${minLabel} `;
  }
  result += `${remainingSeconds}${secLabel}`;

  return result;
};

export const secondsToFormat = (seconds: number, format = 'mm:ss') => {
  return dayjs.duration(seconds, 'seconds').format(format);
};

export const downloadFileFromUrl = (url: string, fileName: string) => {
  if (!url) return;
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();
  link.remove();
};

export const removeTrailingSlash = (url: string | undefined): string => {
  if (!url) return '';
  if (url === '/') return url;
  if (url.endsWith('/')) {
    return url.slice(0, -1);
  }
  return url;
};

export const removeDeviceId = () => {
  return localStorage.removeItem('deviceId');
};

export const getLoginUrl = (basePath?: string) => {
  const port = window?.location?.port ? `:${window.location.port}` : '';
  const redirectUri = window
    ? `${window?.location?.protocol}//${window?.location?.hostname}${port}${basePath}/login`
    : '';
  return redirectUri;
};

export const logout = async (basePath?: string) => {
  try {
    await fireLogout(basePath);
    localStorage.removeItem('cdss-auth-token');
    localStorage.removeItem('gc-access-token');
    localStorage.removeItem('permissions');
    localStorage.removeItem('userName');
    window.location.href = `${basePath}/login`;
    // window.location.reload();
  } catch (error) {
    console.error('Error logging out', error);
    // remove token in local storage even if logout api failed
    localStorage.removeItem('cdss-auth-token');
    localStorage.removeItem('gc-access-token');
    localStorage.removeItem('permissions');
    localStorage.removeItem('userName');
    window.location.href = `${basePath}/login`;
    // window.location.reload();
  }
};

export const extractErrorMessage = (error: any) => {
  return (
    error?.response?.data?.error ??
    error?.response?.data?.message ??
    error?.message ??
    'Uncatgorized error'
  );
};

export const agentStatusColorMap: StatusColorMap = {
  away: '#ffe600',
  break: '#ffe600',
  available: '#1cc500',
  busy: '#ff271c',
  meal: '#ffe600',
  idle: '#1cc500',
  meeting: '#ff271c',
  offline: '#ff271c',
  training: '#ffe600',
  onqueue: '#1cc500',
  'on queue': '#21c0f6',
};

export const routingStatusMap: RoutingStatusMap = {
  idle: '#1cc500',
  off_queue: '#ff271c',
  interacting: '#ffe600',
  communicating: '#ffe600',
  not_responding: '#ff271c',
};

export const extractIdandStatus = (
  event: string
):
  | {
      eventType:
        | 'conversation'
        | 'agent'
        | 'station'
        | 'queue'
        | 'disconnect'
        | 'user'
        | 'conversations'
        | 'queues';
      conversationId?: string;
      eventSubType?: string; // 'state' | 'station' | 'conversation'
      monitor?: string; // 'observe | 'aggregate'
      status: string;
    }
  | undefined => {
  const parts = event.split('.');
  if (parts?.[0] === 'conversation') {
    const conversationIdPart = parts?.[1];

    const statusPart = parts[parts.length - 1];

    const conversationIdRegex = /([a-zA-Z0-9-]+)/;
    const statusRegex = /([a-zA-Z]+)/;

    const conversationIdMatch = conversationIdPart.match(conversationIdRegex);
    const statusMatch = statusPart.match(statusRegex);

    if (conversationIdMatch && statusMatch) {
      return {
        eventType: 'conversation',
        conversationId: conversationIdMatch[1],
        status: statusMatch[1],
      };
    } else {
      throw new Error('Invalid input event');
    }
  } else if (parts?.[0] === 'agent') {
    return {
      eventType: 'agent',
      eventSubType: parts?.[1],
      monitor: parts?.[2],
      status: parts[parts.length - 1],
    };
  } else {
    return undefined;
  }
};

export const validateGlobalPhoneNumber = (phoneNumber: string): boolean => {
  // Regular expression pattern to match global phone numbers
  // allow pattern 6xxxxxxx | +852 6xxxxxxx | +8526xxxxxxx
  const phoneRegex = /^(\+\d{1,3}[\s]?\d{4,15}|\+\d{1,3}\d{4,15}|\d{4,15})$/;

  return phoneRegex.test(phoneNumber);
};

export const formatInteractionTiming = (
  startTime: string,
  endTime: string
): string => {
  let result = '';
  if (!startTime && !endTime) return '';
  if (!startTime) {
    const now = dayjs();
    const end = dayjs(endTime);
    // Calculate startAt
    let endAt: string;
    if (end.isSame(now, 'day')) {
      endAt = end.format('h:mm A');
    } else if (end.isSame(now.subtract(1, 'day'), 'day')) {
      endAt = 'yesterday';
    } else {
      endAt = end.from(now);
    }

    const resultObj = { startAt: `${endAt}`, duration: '' };
    result = `${resultObj.startAt}`;
  } else {
    const start = dayjs(startTime);
    const end = dayjs(endTime);
    const now = dayjs();

    // Calculate startAt
    let startAt: string;
    if (start.isSame(now, 'day')) {
      startAt = start.format('h:mm A');
    } else if (start.isSame(now.subtract(1, 'day'), 'day')) {
      startAt = 'yesterday';
    } else {
      // startAt = start.from(now);
      startAt = start.format(GLOBAL_DATE_FORMAT);
    }

    // Calculate duration
    const interactionDuration = dayjs.duration(end.diff(start));
    let duration: string;
    if (interactionDuration.asSeconds() < 60) {
      duration = `${Math.round(interactionDuration.asSeconds())} seconds`;
    } else {
      duration = interactionDuration.humanize();
    }
    if (!endTime) duration = 'a few seconds';

    const resultObj = { startAt, duration };
    result = `${resultObj.startAt} (${resultObj.duration})`;
  }
  return result;
};

export const get2LettersFromName = (name: string): string => {
  if (!name) return '--';
  const rgx = new RegExp(/(\p{L}{1})\p{L}+/u, 'gu');

  const initials = [...name.matchAll(rgx)];

  return (
    (initials.shift()?.[1] || '') + (initials.pop()?.[1] || '')
  ).toUpperCase();
};
/**
 * Given a time string in the format "HH:MM:SS.SSS", where:
 * - HH: hours
 * - MM: minutes
 * - SS: seconds
 * - SSS: milliseconds
 *
 * Returns the time in seconds as a number.
 *
 * @param {string} timeString - the time string to convert
 * @returns {number} the time in seconds
 */
export const timeStringToSeconds = (timeString: string): number => {
  const [hours, minutes, secondsWithMs] = timeString.split(':');
  const [seconds, milliseconds] = secondsWithMs.split('.');

  let totalSeconds =
    parseInt(hours, 10) * 3600 + // 转换小时为秒
    parseInt(minutes, 10) * 60 + // 转换分钟为秒
    parseInt(seconds, 10); // 秒数
  const millisecondsInNumber = parseInt(milliseconds, 10);
  if (milliseconds && isNaN(millisecondsInNumber)) {
    totalSeconds += millisecondsInNumber / 1000; // 毫秒转
  }

  if (isNaN(totalSeconds)) return 0;
  return totalSeconds;
};
const createResultMap = (prefix: string): Record<string, string> => ({
  pass: `${prefix}.passed`,
  passed: `${prefix}.passed`,
  fail: `${prefix}.failed`,
  failed: `${prefix}.failed`,
  'to be reviewed': `${prefix}.toBeReviewed`,
  'not evaluated': `${prefix}.notEvaluated`,
});

/**
 * Get the translation key for a given evaluation result
 * @param result - The evaluation result
 * @param prefix - The prefix for the translation keys
 * @returns The corresponding translation key or the original result if not found
 */
export const getTranslatedResult = (result: string, prefix: string): string => {
  const resultMap = createResultMap(prefix);
  const normalizedResult = result.toLowerCase();
  return resultMap[normalizedResult] || result;
};
