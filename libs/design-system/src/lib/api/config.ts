const isUAT = process.env['NEXT_PUBLIC_ENVIRONMENT'] !== 'dev';

export const apiConfig = {
  isUAT,
  paths: {
    session: isUAT
      ? '/api/process-api/ctint-session/ctint-bff-cdss/session'
      : '/api/session/ctint-session/ctint-bff-cdss/session',
    logout: `/api/process-api/ctint-auth/logout`,
    callControl: {
      getAllActiveConversations:
        '/api/process-api/ctint-conv/conversation/activeList',
      getAllConversations: '/api/process-api/ctint-conv/conversations',
      action: '/api/process-api/ctint-call-control/conversations/calls',
      userRoutingStatus:
        '/api/process-api/ctint-call-control/routingstatus/users',
      updateRoutingStatus:
        '/api/process-api/ctint-call-control/presences/users',
      getAllAgentStatus: '/api/process-api/ctint-call-control/presences',
      getAllStations: '/api/process-api/ctint-call-control/stations',
      call: '/api/process-api/ctint-call-control/conversations/call',
      blindTransfer:
        '/api/process-api/ctint-call-control/conversations/calls/blind/transfer',
      consult:
        '/api/process-api/ctint-call-control/conversations/calls/consult',
      consultDisconnect:
        '/api/process-api/ctint-call-control/conversations/calls/consult/disconnect',
      consultCancel:
        '/api/process-api/ctint-call-control/conversations/calls/consult/cancel',
      conference:
        '/api/process-api/ctint-call-control/conversations/calls/conference',
      getWrapup:
        '/api/process-api/ctint-call-control/conversations/calls/wrapupcodes',
      submitWrapup:
        '/api/process-api/ctint-call-control/conversations/calls/wrapup/add',
      updateWrapup:
        '/api/process-api/ctint-call-control/conversations/calls/wrapup/update',
      attributes:
        '/api/process-api/ctint-call-control/conversations/calls/attributes',
      getWrapupCategory: '/api/process-api/ctint-call-control/wrapUp',
    },
    users: {
      getAllUsers: '/api/process-api/ctint-user/users', // /api/process-api/ctint-user/users?filterType=all
      getAllWorkgroups: '/api/process-api/ctint-user/queues', // /api/process-api/ctint-user/queues?filterType=all
      searchUsers: '/api/process-api/ctint-user/users/search',
      searchWorkgroups: '/api/process-api/ctint-user/queues/search',
      getWorkGroupsByUser: '/api/process-api/ctint-user/queues',
    },
    miniWallboard: {
      getUserStat:
        '/api/process-api/ctint-user/analytics/users/status/aggregates?type=miniwallboard',
      getUserConversationStat:
        '/api/process-api/ctint-user/analytics/users/converstation/aggregates?type=miniwallboard',
      getQueueStat:
        '/api/process-api/ctint-user/analytics/queues/status/aggregates?type=miniwallboard',
      getQueueConversationStat:
        '/api/process-api/ctint-user/analytics/queues/conversations/aggregates?type=miniwallboard ',
      getUserAggregates:
        '/api/process-api/ctint-user/users/conversations/aggregates?site=miniwallboard',
      getQueueAggregates:
        '/api/process-api/ctint-user/queues/aggregates?site=miniwallboard',
    },
    config: {
      getTenatConfig: '/api/process-api/ctint-config/tenantconfig',
      updateTenantConfig: '/api/process-api/ctint-config/tenantconfig',
    },
  },
};
