import { ReactNode, useEffect } from 'react';
import { cn } from '../../../lib/utils';
import { useTabsContext } from '../../../context/TabsContext';

export type Trigger = {
  value: string;
  label: string;
};

type TabsProps = {
  children?: ReactNode;
  customItems?: ReactNode;
  className?: string;
  triggers: Trigger[];
  rightPanel?: ReactNode;
  defaultTab?: string;
  triggerClassName?: string;
  onChangeTabFunc?: (tab: string) => void;
};

type TabsContentProps = {
  value: string;
  className?: string;
  children?: ReactNode;
  selected?: string;
};

const Tabs = ({
  children,
  className,
  triggers,
  rightPanel,
  customItems,
  defaultTab = '',
  triggerClassName,
  onChangeTabFunc,
}: TabsProps) => {
  const { selected, onChangeTab } = useTabsContext();

  useEffect(() => {
    onChangeTab(defaultTab);
  }, []);

  return (
    <div
      className={cn(
        'flex flex-col w-full h-full rounded-2xl bg-white',
        className
      )}
    >
      <div className="flex w-full justify-between items-center border-b border-grey-200">
        <div className="w-full flex items-center justify-between">
          <div className="flex gap-6 shrink-0 px-4">
            {triggers.map((trigger) => (
              <button
                key={`trigger-${trigger.value}`}
                onClick={() => {
                  onChangeTab(trigger.value);
                  onChangeTabFunc && onChangeTabFunc(trigger.value);
                }}
                className={cn(
                  'p-2 text-remark font-bold',
                  selected === trigger.value
                    ? 'text-black shadow-b shadow-tab-selected'
                    : 'text-grey-500',
                  triggerClassName
                )}
              >
                {trigger.label}
              </button>
            ))}
          </div>
          {rightPanel && <div className="inline-flex mr-4">{rightPanel}</div>}
        </div>
        {customItems}
      </div>
      {children}
    </div>
  );
};

const TabsContent = ({
  value,
  className,

  children,
}: TabsContentProps) => {
  const { selected } = useTabsContext();
  if (value !== selected) return null;

  return <div className={cn('p-2', className)}>{children}</div>;
};

export { Tabs, TabsContent };
